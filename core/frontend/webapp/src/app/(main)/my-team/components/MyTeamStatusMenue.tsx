"use client";

import { useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { AttendanceStatus } from "../store/myTeamStore";
import CustomIcon from "@/components/common/CustomIcons";
import { UserRole } from "@/enum/rolesEnum";
import { resolveUserRole } from "@/utils/userRoleHelper";
import useMockAuthStore from "@/store/mockAuthStore";

interface AttendanceStatusMenuProps {
  onSelect: (status: AttendanceStatus) => void;
  onClose: () => void;
  position: { x: number; y: number };
  onOpenRequestDialog: () => void;
  showMoreDetails?: boolean;
  replace?: boolean;
  onOpenAttendanceSheetDialog?: () => void;
  onOpenReplaceDialog?: () => void;
}

export function MyTeamStatusMenue({
  onSelect,
  onClose,
  position,
  onOpenRequestDialog,
  showMoreDetails = true,
  replace,
  onOpenAttendanceSheetDialog,
  onOpenReplaceDialog,
}: AttendanceStatusMenuProps) {
  const menuRef = useRef<HTMLDivElement>(null);
  const { currentUser: user } = useMockAuthStore();
  const userRole = resolveUserRole(user);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [onClose]);

  return (
    <>
      <div
        ref={menuRef}
        className="absolute bg-white shadow-lg border border-gray-200 rounded-md z-50 w-30"
        style={{
          top: `${position.y}px`,
          left: `${position.x}px`,
        }}
      >
        <div className="p-1">
          {/* Always show P option */}
          <Button
            variant="ghost"
            size="sm"
            className="w-full justify-center text-left mb-1 text-[#4CAF50] border  border-[#9D9D9D] bg-[#EBF6EC] hover:bg-green-200 hover:text-[#4CAF50]"
            onClick={() => onSelect("P")}
          >
            P
          </Button>
          {/* <Button
            variant="ghost"
            size="sm"
            className="w-full justify-center text-left mb-1 text-[#1976D2] border  border-[#9D9D9D] bg-[#E3F2FD] hover:bg-blue-200 hover:text-[#1976D2]"
            onClick={() => onSelect("P_IN_BUS")}
          >
            P_IN_BUS
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="w-full justify-center text-left mb-1 text-[#2E7D32] border  border-[#9D9D9D] bg-[#E8F5E8] hover:bg-green-200 hover:text-[#2E7D32]"
            onClick={() => onSelect("P_IN_PLANT")}
          >
            P_IN_PLANT
          </Button> */}
          {!replace && (
            <Button
              variant="ghost"
              size="sm"
              className="w-full justify-center text-left mb-1 text-[#D60000] border  border-[#9D9D9D] bg-[#D600001C] hover:bg-red-200 hover:text-[#D60000]"
              onClick={() => onSelect("AB")}
            >
              AB
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            className="w-full justify-center text-[#D60000] text-left mb-1 border border-[#9D9D9D] bg-[#D600001C] hover:bg-red-200 hover:text-[#D60000]"
            onClick={() => onSelect("CTN")}
          >
            CTN
          </Button>{" "}
          {!replace && (
            <Button
              variant="ghost"
              size="sm"
              className="w-full justify-center text-[#010101] text-left mb-1 border border-[#9D9D9D] bg-[#DAE8F9] hover:bg-blue-200"
              onClick={onOpenRequestDialog}
            >
              Raise a request
            </Button>
          )}
          {showMoreDetails && (
            <Button
              variant="ghost"
              size="sm"
              className="w-full justify-start text-[#010101] text-left mb-1 border border-[#9D9D9D] bg-[#FFFFFF] hover:bg-gray-200"
              onClick={() => {
                if (onOpenAttendanceSheetDialog) {
                  onOpenAttendanceSheetDialog();
                } else {
                  onClose();
                }
              }}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-3 w-3"
              >
                <path d="M5 12h14" />
                <path d="M12 5v14" />
              </svg>{" "}
              <span className="">More details...</span>
            </Button>
          )}
          {userRole === UserRole.TEAM_LEADER && replace && (
            <Button
              variant="ghost"
              size="sm"
              className="w-full justify-start text-[#010101] text-left mb-1 border border-[#9D9D9D] bg-[#EBF6EC] hover:bg-green-200"
              onClick={() => {
                if (onOpenReplaceDialog) {
                  onOpenReplaceDialog();
                }
                onClose();
              }}
            >
              <CustomIcon
                name="replace"
                className=""
                style={{ width: "12px", height: "12px" }}
              />
              Replace
            </Button>
          )}
        </div>
      </div>
    </>
  );
}
