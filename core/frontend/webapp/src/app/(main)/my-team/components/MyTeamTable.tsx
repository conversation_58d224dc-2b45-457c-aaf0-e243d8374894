"use client";

import type React from "react";
import { useMemo, useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  AttendanceStatus,
  useAttendanceStore,
  ClockingValidationStatus,
  TeamStatus,
} from "../store/myTeamStore";
import { useTeamLeaderStore } from "../store/teamLeaderStore";
import { MyTeamStatusMenue } from "./MyTeamStatusMenue";
import { RequestDialog } from "./dialogs/RequestDialog";
import { RequestFormDialog } from "./dialogs/RequestFormDialog";
import { Checkbox } from "@/components/ui/checkbox";
import { DetailsDialog } from "./dialogs/DetailsDialog";
import { UserRole } from "@/enum/rolesEnum";
import { useMockAuthStore } from "@/store/mockAuthStore";
import { resolveUserRole } from "@/utils/userRoleHelper";
import { DatePicker } from "@/components/ui/datePicker";
import CustomSelect from "@/components/common/CustomSelect";
import { ClockingValidationDialog } from "./dialogs/ClockingValidationDialog";
import CustomIcon from "@/components/common/CustomIcons";
import { ChevronLeft } from "lucide-react";
import { SendForReviewDialog } from "./dialogs/SendForReviewDialog";
import { ReplaceDialog } from "./dialogs/ReplaceDialog";
import { Operator } from "../store/replacementStore";
import useReplacementStore from "../store/replacementStore";

// Helper to safely get string or empty
const safeString = (val: string | undefined) => val || "";

export function MyTeamTable({
  activeTab,
  selectedWorkers,
  setSelectedWorkers,
  filteredWorkersLength,
}: {
  activeTab: string;
  selectedWorkers: Set<string>;
  setSelectedWorkers: React.Dispatch<React.SetStateAction<Set<string>>>;
  filteredWorkersLength: number;
}) {
  const { currentUser: user } = useMockAuthStore();
  const {
    workers,
    filterMfgOnly,
    currentDate,
    markAttendance,
    getAttendanceForWorkerAndDate,
    attendanceSheetDetails,
    setAttendanceSheetDetails,
    selectedValidationTeam,
    updateTeamStatus,
    isVisualCheckActive,
  } = useAttendanceStore();

  // Team Leader specific store
  const {
    teamData,
    statusMonitoringData,
    // currentStatusData,
    isTeamDataLoading,
    isStatusMonitoringLoading,
    isCurrentStatusLoading,
    // teamDataError,
    // currentStatusError,
    updateOperatorStatus,
    updateOperatorAttendanceStatus,
    statusInfoList,
  } = useTeamLeaderStore();

  // Get user role from auth store
  const userRole = resolveUserRole(user);
  const [statusMenuInfo, setStatusMenuInfo] = useState<{
    isOpen: boolean;
    position: { x: number; y: number };
    workerId: string;
    date: string;
    sequenceMode?: boolean;
    replace?: boolean;
  } | null>(null);
  const [isRequestDialogOpen, setIsRequestDialogOpen] = useState(false);
  const [isRequestFormDialogOpen, setIsRequestFormDialogOpen] = useState(false);
  const [selectedRequestType, setSelectedRequestType] = useState<
    | {
        id: string;
        title: string;
      }
    | undefined
  >(undefined);
  const [attendanceDialogInfo, setAttendanceDialogInfo] = useState<{
    isOpen: boolean;
    worker: { id: string; name: string; mle: string } | null;
    date: Date | null;
  }>({ isOpen: false, worker: null, date: null });
  const containerRef = useRef<HTMLDivElement>(null);
  const [isClockingValidationDialogOpen, setIsClockingValidationDialogOpen] =
    useState(false);
  const [selectedWorkerId, setSelectedWorkerId] = useState<string | null>(null);
  const [selectedValidationWorkerId, setSelectedValidationWorkerId] = useState<
    string | null
  >(null);
  const [isSendForReviewDialogOpen, setIsSendForReviewDialogOpen] =
    useState(false);

  // State for real-time updates
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update current time every second for real-time counters
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleOpenSendForReviewDialog = () => {
    setIsSendForReviewDialogOpen(true);
  };

  // Filter workers based on role, MFG filter, and clocking category
  const filteredWorkers = useMemo(() => {
    // For Team Leader role, use status monitoring data directly
    if (userRole === UserRole.TEAM_LEADER) {
      const selectedTeam =
        useAttendanceStore.getState().teamLeaderFilters.selectedTeam;

      // Get operators directly from status monitoring data
      const statusOperators = statusMonitoringData?.status_data || [];



      console.log("statusOperators", statusOperators);
      // Convert status monitoring operators to worker format
      let workers = statusOperators.map((operator) => ({
        id: operator.legacy_id.toString(),
        uuid : operator.id,
        mle: operator.legacy_id.toString(),
        firstName: operator.first_name,
        lastName: operator.last_name,
        role: operator.role,
        function: operator.role, // Use role as function
        team: operator.team_id,
        line: undefined,
        status: undefined, // Will be determined by getCurrentStatusForOperator
        clockingValidation: "Not finished yet" as ClockingValidationStatus,
      }));

      // Filter by selected team if one is chosen
      if (selectedTeam) {
        workers = workers.filter((worker) => worker.team === selectedTeam);
      }

      // Filter by MFG if enabled
      if (filterMfgOnly) {
        workers = workers.filter(
          (worker) => worker.role.toLowerCase() === "mfg structure",
        );
      }

      return workers;
    }

    // For other roles, use existing workers (they have their own data sources)
    let filtered = workers;

    // Filter by MFG if enabled (for Team Leader)
    if (filterMfgOnly) {
      filtered = filtered.filter(
        (worker) => worker.role.toLowerCase() === "mfg structure",
      );
    }

    // Filter by clocking category for Department Clerk and Department Manager
    if (
      userRole === UserRole.DEPARTEMENT_CLERK ||
      userRole === UserRole.DEPARTEMENT_MANAGER
    ) {
      if (activeTab === "clocking-ih") {
        filtered = filtered.filter((worker) => worker.category === "IH");
      } else if (activeTab === "clocking-is") {
        filtered = filtered.filter((worker) => worker.category === "IS");
      }
    }

    return filtered;
  }, [workers, filterMfgOnly, userRole, activeTab, statusMonitoringData]);

  // Generate calendar days - for Team Leader use API date range, for others use current month
  const calendarDays = useMemo(() => {
    if (userRole === UserRole.TEAM_LEADER && statusMonitoringData?.status_data && statusMonitoringData.status_data.length > 0) {
      // Get unique dates from the status monitoring data
      const allDates = new Set<string>();
      statusMonitoringData.status_data.forEach(operator => {
        operator.dailyRecords.forEach(record => {
          allDates.add(record.date);
        });
      });

      // Add today and next 3 days if not already present
      const today = new Date();
      for (let i = 0; i < 4; i++) {
        const date = new Date(today);
        date.setDate(today.getDate() + i);
        const dateStr = date.toISOString().split("T")[0];
        allDates.add(dateStr);
      }

      const sortedDates = Array.from(allDates).sort();
      return sortedDates.map(dateStr => {
        const date = new Date(dateStr + 'T00:00:00');
        return {
          day: date.getDate(),
          date: dateStr,
          dayName: date.toLocaleDateString("en-US", { weekday: "short" }),
          isToday: date.toDateString() === new Date().toDateString(),
          isFutureStatic: date > new Date() && date <= new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // Next 3 days
        };
      });
    }

    // For other roles, use current month as before
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    return Array.from({ length: daysInMonth }, (_, i) => {
      const day = i + 1;
      const date = new Date(year, month, day);
      return {
        day,
        date: date.toISOString().split("T")[0],
        dayName: date.toLocaleDateString("en-US", { weekday: "short" }),
        isToday: date.toDateString() === new Date().toDateString(),
        isFutureStatic: false,
      };
    });
  }, [userRole, statusMonitoringData, currentDate]);

  const getStatusColor = (status: AttendanceStatus) => {
    switch (status) {
      case "P":
        return "bg-[#4CAF501C] text-[#4CAF50] hover:bg-green-100 hover:text-[#4CAF50]";
      case "P_IN_BUS":
        return "bg-[#E3F2FD] text-[#1976D2] hover:bg-blue-100 hover:text-[#1976D2]";
      case "P_IN_PLANT":
        return "bg-[#E8F5E8] text-[#2E7D32] hover:bg-green-100 hover:text-[#2E7D32]";
      case "CTN":
        return "bg-[#FBE3E3] text-[#D60000] hover:bg-red-200 hover:text-[#D60000]";
      case "MA":
        return "bg-[#FBE3E3] text-[#D60000] hover:bg-red-200 hover:text-[#D60000]";
      case "AB":
        return "bg-[#FBE3E3] text-[#D60000] hover:bg-red-200 hover:text-[#D60000]";
      default:
        return "bg-white hover:bg-gray-50";
    }
  };

  // Helper function to format time from ISO string
  const formatTime = (isoString: string) => {
    const date = new Date(isoString);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  // Helper function for shift status
  const getShiftStatus = () => {
    return statusMonitoringData?.shift_status || "INIT";
  };

  // Helper function to get status color from API data
  const getStatusColorFromAPI = (statusCode: string) => {
    const statusInfo = statusInfoList.find(info => info.statusCode === statusCode);
    if (statusInfo) {
      return `bg-[${statusInfo.color}] text-white`;
    }
    // Fallback to existing color logic
    return getStatusColor(statusCode as AttendanceStatus);
  };

  const getClockingValidationColor = (status: ClockingValidationStatus) => {
    switch (status) {
      case "Validated":
        return "text-green-600";
      case "Waiting validation...":
        return "text-orange-600 cursor-pointer hover:text-orange-700";
      case "Absent":
        return "text-red-600 cursor-pointer hover:text-red-700";
      case "Not finished yet":
        return "text-gray-500";
      default:
        return "text-gray-500";
    }
  };

  const handleCellClick = (
    workerId: string,
    date: string,
    isToday: boolean,
    event: React.MouseEvent,
  ) => {
    if (!isToday) return;

    // For Team Leader role, handle differently
    if (userRole === UserRole.TEAM_LEADER && statusMonitoringData?.status_data) {
      const operator = statusMonitoringData.status_data.find(
        (op) => op.legacy_id.toString() === workerId
      );

      if (operator) {
        // Find the daily record for this specific date
        const dailyRecord = operator.dailyRecords.find(record => record.date === date);
        let currentStatus: AttendanceStatus = "";

        if (dailyRecord && dailyRecord.statusCodes.length > 0) {
          // Get the latest status code for this date
          const latestStatus = dailyRecord.statusCodes[dailyRecord.statusCodes.length - 1];
          currentStatus = latestStatus.code as AttendanceStatus;
        }

        if (currentStatus === "P" || currentStatus === "P_IN_BUS" || currentStatus === "P_IN_PLANT" || currentStatus === "AB") {
          // Open the status menu
          const cellRect = (
            event.currentTarget as HTMLElement
          ).getBoundingClientRect();
          const containerRect = containerRef.current?.getBoundingClientRect();
          let x = cellRect.left;
          let y = cellRect.bottom;
          if (containerRect && containerRef.current) {
            const MENU_OFFSET_X = 0;
            const MENU_OFFSET_Y = 0;
            x =
              cellRect.left -
              containerRect.left +
              containerRef.current.scrollLeft +
              cellRect.width +
              MENU_OFFSET_X;
            y =
              cellRect.bottom -
              containerRect.top +
              containerRef.current.scrollTop +
              MENU_OFFSET_Y;
          }
          setStatusMenuInfo({
            isOpen: true,
            position: { x, y },
            workerId,
            date,
            replace: currentStatus === "AB",
          });
          return;
        }

        // If no status and we're in VISUAL_CHECK mode, show attendance options
        const shiftStatus = getShiftStatus();
        if (shiftStatus === "VISUAL_CHECK" && currentStatus === "") {
          // Open the status menu for attendance marking
          const cellRect = (
            event.currentTarget as HTMLElement
          ).getBoundingClientRect();
          const containerRect = containerRef.current?.getBoundingClientRect();
          let x = cellRect.left;
          let y = cellRect.bottom;
          if (containerRect && containerRef.current) {
            const MENU_OFFSET_X = 0;
            const MENU_OFFSET_Y = 0;
            x =
              cellRect.left -
              containerRect.left +
              containerRef.current.scrollLeft +
              cellRect.width +
              MENU_OFFSET_X;
            y =
              cellRect.bottom -
              containerRect.top +
              containerRef.current.scrollTop +
              MENU_OFFSET_Y;
          }
          setStatusMenuInfo({
            isOpen: true,
            position: { x, y },
            workerId,
            date,
            replace: false,
          });
          return;
        }

        // If not P or AB, update to P (for other statuses)
        const shiftInstantId = statusMonitoringData?.shift_instant_id || teamData?.shiftInstantId;
        if (shiftInstantId && currentStatus !== "") {
          updateOperatorStatus(shiftInstantId, operator.legacy_id, "P");
        }
        return;
      }
    }

    // For other roles, use existing logic
    const currentStatus = getAttendanceForWorkerAndDate(workerId, date);
    const isSequence =
      typeof currentStatus === "string" && currentStatus.includes(",");

    if (currentStatus === "P" || currentStatus === "AB" || isSequence) {
      // Open the status menu
      const cellRect = (
        event.currentTarget as HTMLElement
      ).getBoundingClientRect();
      const containerRect = containerRef.current?.getBoundingClientRect();
      let x = cellRect.left;
      let y = cellRect.bottom;
      if (containerRect && containerRef.current) {
        const MENU_OFFSET_X = 0;
        const MENU_OFFSET_Y = 0;
        x =
          cellRect.left -
          containerRect.left +
          containerRef.current.scrollLeft +
          cellRect.width +
          MENU_OFFSET_X;
        y =
          cellRect.bottom -
          containerRect.top +
          containerRef.current.scrollTop +
          MENU_OFFSET_Y;
      }
      setStatusMenuInfo({
        isOpen: true,
        position: { x, y },
        workerId,
        date,
        replace: currentStatus === "AB",
      });
      return;
    }

    // If not P or sequence, always revert to P on click
    markAttendance(workerId, date, "P");
  };

  const handleStatusSelect = (status: AttendanceStatus) => {
    if (statusMenuInfo) {
      console.log("Setting sequence mode for status:", status);

      // For Team Leader role, use status monitoring data
      if (userRole === UserRole.TEAM_LEADER && statusMonitoringData?.status_data) {
        const operator = statusMonitoringData.status_data.find(
          (op) => op.legacy_id.toString() === statusMenuInfo.workerId,
        );


        console.log("operator", operator);
        const shiftInstantId = statusMonitoringData?.shift_instant_id || teamData?.shiftInstantId;
        const shiftStatus = getShiftStatus();

        if (operator && shiftInstantId) {
          // Use the correct API call based on shift status
          if (shiftStatus === "VISUAL_CHECK" && (status === "P" || status === "AB")) {
            updateOperatorAttendanceStatus(shiftInstantId, operator.id, status);
          } else {
            updateOperatorStatus(shiftInstantId, operator.legacy_id, status);
          }
          closeStatusMenu();
          return;
        }
      }

      // For other roles, use existing logic
      if (statusMenuInfo.sequenceMode) {
        // Set all hours (6-13) to the selected status as a sequence (comma-separated)
        const details = Array(8).fill(status); // 8 hours: 6-13
        setAttendanceSheetDetails(
          statusMenuInfo.workerId,
          statusMenuInfo.date,
          details,
        );
        closeStatusMenu();
        return;
      }
      // For single status, always set as a plain string (not a sequence)
      markAttendance(statusMenuInfo.workerId, statusMenuInfo.date, status);
      // Clear any sequence for this cell/date
      setAttendanceSheetDetails(
        statusMenuInfo.workerId,
        statusMenuInfo.date,
        [],
      );
      closeStatusMenu();
    }
  };

  const closeStatusMenu = () => {
    setStatusMenuInfo(null);
  };

  const handleOpenRequestDialog = () => {
    setIsRequestDialogOpen(true);
    closeStatusMenu();
  };

  const handleCloseRequestDialog = () => {
    setIsRequestDialogOpen(false);
  };

  const handleOpenRequestForm = (requestType: {
    id: string;
    title: string;
  }) => {
    setSelectedRequestType(requestType);
    setIsRequestFormDialogOpen(true);
    setIsRequestDialogOpen(false);
  };

  const handleCloseRequestFormDialog = () => {
    setIsRequestFormDialogOpen(false);
    setSelectedRequestType(undefined);
  };

  const handleBackToRequestList = () => {
    setIsRequestFormDialogOpen(false);
    setIsRequestDialogOpen(true);
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedWorkers(new Set(filteredWorkers.map((w) => w.mle)));
    } else {
      setSelectedWorkers(new Set());
    }
  };

  const handleSelectWorker = (workerId: string, checked: boolean) => {
    const newSelected = new Set(selectedWorkers);
    if (checked) {
      newSelected.add(workerId);
    } else {
      newSelected.delete(workerId);
    }
    setSelectedWorkers(newSelected);
  };

  const handleOpenAttendanceSheetDialog = () => {
    if (statusMenuInfo) {
      const worker = workers.find((w) => w.id === statusMenuInfo.workerId);
      if (worker) {
        setAttendanceDialogInfo({
          isOpen: true,
          worker: {
            id: worker.id,
            name: `${worker.firstName} ${worker.lastName}`,
            mle: worker.mle || "",
          },
          date: new Date(statusMenuInfo.date),
        });
      }
    }
    closeStatusMenu();
  };

  const handleCloseAttendanceSheetDialog = () => {
    setAttendanceDialogInfo({ isOpen: false, worker: null, date: null });
  };

  const renderAttendanceCell = (
    workerId: string,
    date: string,
    isToday: boolean,
    readOnly: boolean = false,
  ) => {
    const key = `${workerId}_${date}`;
    const sequence = attendanceSheetDetails[key];
    const CHUNK_SIZE = 4;


    // For Team Leader role, use status monitoring data directly
    if (userRole === UserRole.TEAM_LEADER && statusMonitoringData?.status_data) {
      // Find the operator by legacy_id (converted to string as workerId)
      const operator = statusMonitoringData.status_data.find(
        (op) => op.legacy_id.toString() === workerId
      );

      if (operator) {
        // Find the daily record for this specific date
        const dailyRecord = operator.dailyRecords.find(record => record.date === date);

        if (dailyRecord && dailyRecord.statusCodes.length > 0) {
          // If there's only one status code, show it directly
          if (dailyRecord.statusCodes.length === 1) {
            const statusCode = dailyRecord.statusCodes[0];
            const status = statusCode.code as AttendanceStatus;
            const statusBgColor = getStatusColorFromAPI(status);
            const statusTime = formatTime(statusCode.startDateTime);

            if (readOnly) {
              return (
                <div
                  className={`w-full h-12 flex flex-col items-center justify-center text-xs font-medium ${statusBgColor}`}
                  title={`Status: ${status} at ${statusTime}`}
                >
                  <div className="font-bold">{status}</div>
                  <div className="text-xs opacity-75">{statusTime}</div>
                </div>
              );
            }

            return (
              <Button
                variant="ghost"
                size="sm"
                className={`w-full h-12 text-xs font-medium border-0 rounded-none ${statusBgColor} ${
                  isToday ? "cursor-pointer" : "cursor-not-allowed opacity-60"
                }`}
                onClick={(e) => handleCellClick(workerId, date, isToday, e)}
                disabled={!isToday}
                title={`Status: ${status} at ${statusTime}`}
              >
                <div className="flex flex-col items-center">
                  <div className="font-bold">{status}</div>
                  <div className="text-xs opacity-75">{statusTime}</div>
                </div>
              </Button>
            );
          }

          // Multiple status codes - show them in compact lines with latest status background
          const statusCodes = dailyRecord.statusCodes;
          const latestStatus = statusCodes[statusCodes.length - 1];
          const latestStatusBgColor = getStatusColor(latestStatus.code as AttendanceStatus);

          // Split status codes into lines of 3
          const CHUNK_SIZE = 3;
          const lines: typeof statusCodes[] = [];
          for (let i = 0; i < statusCodes.length; i += CHUNK_SIZE) {
            lines.push(statusCodes.slice(i, i + CHUNK_SIZE));
          }

          return (
            <div
              className={`w-full h-12 flex flex-col items-center justify-center text-xs font-medium border-0 rounded-none ${latestStatusBgColor} ${
                isToday && !readOnly ? "cursor-pointer" : "cursor-not-allowed opacity-60"
              } overflow-hidden px-1`}
              onClick={(e) => !readOnly && handleCellClick(workerId, date, isToday, e)}
              title={`Status changes: ${statusCodes.map(sc => `${sc.code} at ${formatTime(sc.startDateTime)}`).join(', ')}`}
            >
              {lines.map((line, lineIdx) => (
                <div key={lineIdx} className="flex items-center justify-center">
                  {line.map((statusCode, idx) => (
                    <span key={idx} className="flex items-center">
                      <span
                        className={`text-xs font-bold ${
                          statusCode.code === "P" || statusCode.code === "P_IN_BUS" || statusCode.code === "P_IN_PLANT"
                            ? "text-green-800"
                            : statusCode.code === "CTN" || statusCode.code === "AB"
                              ? "text-red-800"
                              : "text-gray-800"
                        }`}
                      >
                        {statusCode.code}
                      </span>
                      {idx < line.length - 1 && (
                        <span className="text-gray-800 mx-1">,</span>
                      )}
                    </span>
                  ))}
                  {lineIdx < lines.length - 1 && <span className="text-gray-800">,</span>}
                </div>
              ))}
            </div>
          );
        } else {
          // No status codes for this date - show interactive cell for visual check
          if (isToday && userRole === UserRole.TEAM_LEADER) {
            const shiftStatus = getShiftStatus();

            if (shiftStatus === "VISUAL_CHECK") {
              return (
                <div
                  className="w-full h-12 flex flex-col items-center justify-center text-xs font-medium bg-white border cursor-pointer hover:bg-gray-50"
                  onClick={(e) => handleCellClick(workerId, date, isToday, e)}
                  title="Visual Check Active - Click to mark attendance"
                >
                  <div className="text-gray-600 font-medium text-center leading-tight">
                    -
                  </div>
                </div>
              );
            }
          }

          // Default empty state
          return (
            <div className="w-full h-12 flex items-center justify-center text-xs font-medium bg-white">
              -
            </div>
          );
        }
      }
    }

    // For non-today dates, use status history data
    if (!isToday) {
      let statusHistory: AttendanceStatus;

      // For other roles, use existing attendance data
      if (userRole !== UserRole.TEAM_LEADER) {
        statusHistory = getAttendanceForWorkerAndDate(workerId, date);
      } else {
        statusHistory = "";
      }

      return (
        <div
          className={`w-full h-12 flex items-center justify-center text-xs font-medium ${getStatusColor(statusHistory)}`}
        >
          {statusHistory}
        </div>
      );
    }

    if (sequence && sequence.length > 0) {
      // If all values in the sequence are the same and not in readOnly mode, show only that value as a clickable button
      if (!readOnly && sequence.every((v) => v === sequence[0])) {
        const status = sequence[0];
        return (
          <Button
            variant="ghost"
            size="sm"
            className={`w-full h-12 text-xs font-medium border-0 rounded-none ${getStatusColor(status as AttendanceStatus)} ${
              isToday ? "cursor-pointer" : "cursor-not-allowed opacity-60"
            }`}
            onClick={(e) => handleCellClick(workerId, date, isToday, e)}
            disabled={!isToday}
          >
            {status}
          </Button>
        );
      }
      // Otherwise, show the sequence as before
      const lines: string[][] = [];
      for (let i = 0; i < sequence.length; i += CHUNK_SIZE) {
        lines.push(sequence.slice(i, i + CHUNK_SIZE));
      }
      return (
        <div
          className={`flex flex-col items-center justify-center gap-0.5 min-h-[48px] ${readOnly ? "" : "cursor-pointer"}`}
          {...(!readOnly && {
            onClick: (e: React.MouseEvent) => {
              if (!isToday) return;
              const cellRect = (
                e.currentTarget as HTMLElement
              ).getBoundingClientRect();
              const containerRect =
                containerRef.current?.getBoundingClientRect();
              let x = cellRect.left;
              let y = cellRect.bottom;
              if (containerRect && containerRef.current) {
                x =
                  cellRect.left -
                  containerRect.left +
                  containerRef.current.scrollLeft +
                  cellRect.width;
                y =
                  cellRect.bottom -
                  containerRect.top +
                  containerRef.current.scrollTop;
              }
              setStatusMenuInfo({
                isOpen: true,
                position: { x, y },
                workerId,
                date,
                sequenceMode: true,
              });
            },
          })}
        >
          {lines.map((line, lineIdx) => (
            <div
              key={lineIdx}
              className="flex flex-wrap items-center justify-center"
            >
              {line.map((status, idx) => (
                <span
                  key={idx}
                  className={`text-xs font-bold ${
                    status === "P"
                      ? "text-green-600"
                      : status === "CTN" || status === "AB"
                        ? "text-red-600"
                        : "text-gray-600"
                  }`}
                  style={{ marginRight: idx < line.length - 1 ? 2 : 0 }}
                >
                  {status}
                  {idx < line.length - 1 ? (
                    <span className="text-black">, </span>
                  ) : null}
                </span>
              ))}
            </div>
          ))}
        </div>
      );
    }
    // fallback to default button
    let status: AttendanceStatus;

    // For Team Leader role, use status monitoring data
    if (userRole === UserRole.TEAM_LEADER && statusMonitoringData?.status_data) {
      const operator = statusMonitoringData.status_data.find(
        (op) => op.legacy_id.toString() === workerId
      );

      if (operator) {
        // Find the daily record for this specific date
        const dailyRecord = operator.dailyRecords.find(record => record.date === date);

        if (dailyRecord && dailyRecord.statusCodes.length > 0) {
          // Get the latest status code for this date
          const latestStatus = dailyRecord.statusCodes[dailyRecord.statusCodes.length - 1];
          status = latestStatus.code as AttendanceStatus;
        } else {
          status = "";
        }
      } else {
        status = "";
      }
    } else {
      status = getAttendanceForWorkerAndDate(workerId, date);
    }

    if (readOnly) {
      return (
        <div
          className={`w-full h-12 flex items-center justify-center text-xs font-medium ${getStatusColor(status)}`}
        >
          {status}
        </div>
      );
    }
    return (
      <Button
        variant="ghost"
        size="sm"
        className={`w-full h-12 text-xs font-medium border-0 rounded-none ${getStatusColor(status)} ${
          isToday ? "cursor-pointer" : "cursor-not-allowed opacity-60"
        }`}
        onClick={(e) => handleCellClick(workerId, date, isToday, e)}
        disabled={!isToday}
      >
        {status}
      </Button>
    );
  };

  // Handler for clicking a team in clocking validation column
  const handleClockingValidationCellClick = (
    team: TeamStatus,
    workerId: string,
  ) => {
    setSelectedWorkerId(workerId);
    setIsClockingValidationDialogOpen(true);
    setSelectedValidationWorkerId(workerId);
  };

  // Handler for closing the dialog
  const handleCloseClockingValidationDialog = () => {
    setIsClockingValidationDialogOpen(false);
    setSelectedWorkerId(null);
  };

  // Handler for confirm button in special view
  const handleConfirmSpecialView = () => {
    if (selectedValidationWorkerId && selectedValidationTeam) {
      updateTeamStatus(
        selectedValidationWorkerId,
        selectedValidationTeam.teamId,
        "Validated",
      );
    }
    useAttendanceStore.getState().setSelectedValidationTeam(null);
    setSelectedValidationWorkerId(null);
  };
  // Replace dialog
  const [isReplaceDialogOpen, setIsReplaceDialogOpen] = useState(false);
  const [replaceWorkerMle, setReplaceWorkerMle] = useState<string | null>(null);
  const [absentEmployeeOperator, setAbsentEmployeeOperator] = useState<
    Operator | undefined
  >(undefined);
  const [isLoadingOperator, setIsLoadingOperator] = useState(false);
  const [operatorError, setOperatorError] = useState<string | null>(null);

  const { fetchOperatorById } = useReplacementStore();

  const handleOpenReplaceDialog = () => {
    // Get the worker ID from the status menu context and find the corresponding MLE
    if (statusMenuInfo) {
      const worker = workers.find((w) => w.id === statusMenuInfo.workerId);
      if (worker) {
        setReplaceWorkerMle(worker.mle); // Use MLE instead of ID
      }
    }
    setIsReplaceDialogOpen(true);
    // Reset states when opening dialog
    setAbsentEmployeeOperator(undefined);
    setOperatorError(null);
  };

  // Fetch operator data when replace dialog opens and workerId is available
  useEffect(() => {
    const fetchAbsentEmployee = async () => {
      if (isReplaceDialogOpen && replaceWorkerMle) {
        setIsLoadingOperator(true);
        setAbsentEmployeeOperator(undefined);
        setOperatorError(null);

        try {
          // Use the worker's MLE value for the API call
          const operatorId = replaceWorkerMle; // This is now the MLE value
          const teamLeaderId = "3e2bac24-5866-4065-8a7b-914c2e077cf1";
          const shiftId = "b4bedff2-165e-4156-969f-d3b3cd025970";

          const operatorData = await fetchOperatorById(
            operatorId,
            teamLeaderId,
            shiftId,
          );

          if (operatorData) {
            setAbsentEmployeeOperator(operatorData);
          } else {
            setOperatorError("No operator data found for this employee");
          }
        } catch (error) {
          console.error("Failed to fetch operator data:", error);
          setOperatorError("Failed to load operator data. Please try again.");
        } finally {
          setIsLoadingOperator(false);
        }
      }
    };

    fetchAbsentEmployee();
  }, [isReplaceDialogOpen, replaceWorkerMle, fetchOperatorById]);

  // Copy icons from MyTeamHeader
  const ProjectIcons = (
    <CustomIcon
      name="project"
      className="mr-2"
      style={{ width: "17px", height: "18px", fill: "#000000" }}
    />
  );
  const FamilyIcons = (
    <CustomIcon
      name="family"
      className="mr-2"
      style={{ width: "17px", height: "18px", fill: "#9E7A00" }}
    />
  );
  const LineChartIcons = (
    <CustomIcon
      name="lineChart"
      className="mr-2"
      style={{ width: "17px", height: "18px", fill: "#4CAF50" }}
    />
  );
  const AreaIcons = (
    <CustomIcon
      name="areaChart"
      className="mr-2"
      style={{ width: "17px", height: "18px", fill: "#c59800" }}
    />
  );
  const TeamIcons = (
    <CustomIcon
      name="team"
      className="mr-2"
      style={{ width: "17px", height: "18px", fill: "#8804A1" }}
    />
  );
  const CustomerIcons = (
    <CustomIcon
      name="customer"
      className="mr-2"
      style={{ width: "17px", height: "18px", fill: "#ffb72f" }}
    />
  );

  // Render the shift leader team sheet view (header + read-only table)
  if (selectedValidationTeam && !isClockingValidationDialogOpen) {
    // For demo, filter workers by team (match by team name or team id, fallback to all workers)
    let filteredWorkers = workers;
    if (selectedValidationTeam) {
      filteredWorkers = workers.filter(
        (w) =>
          w.team &&
          (w.team === selectedValidationTeam.teamName ||
            w.team === selectedValidationTeam.teamId),
      );
      if (filteredWorkers.length === 0) {
        // fallback: show all workers if no match
        filteredWorkers = workers;
      }
    }
    // Header
    // For demo, filter workers by team
    // Use the same table as the team leader, but hide the checkbox
    return (
      <div className="flex flex-col gap-2">
        {/* Header - filters row styled like Team Leader view */}
        <div className="flex items-center gap-2 mb-2">
          <div className="bg-[#F5FBFF] border border-[#87B0E6] rounded-lg p-2 flex-shrink-0 mr-2">
            <button
              onClick={() =>
                useAttendanceStore.getState().setSelectedValidationTeam(null)
              }
              className="p-2 rounded hover:bg-gray-200 flex items-center"
              style={{ minWidth: 40 }}
              aria-label="Return"
            >
              <ChevronLeft className="w-5 h-5" />
            </button>
          </div>
          <div className="bg-[#F5FBFF] border border-[#87B0E6] rounded-lg p-2 flex-grow">
            <div className="flex items-center gap-2 flex-nowrap h-full">
              <div className="flex-1 min-w-[200px]">
                <DatePicker
                  label="Date"
                  placeholder="Select a date"
                  className="pointer-events-none opacity-60"
                />
              </div>
              <div className="flex-1 min-w-[200px]">
                <CustomSelect
                  options={[]}
                  onValueChange={() => {}}
                  placeholder="Select customer"
                  label={
                    <span className="flex items-center">
                      {CustomerIcons} Customer
                    </span>
                  }
                  disabled
                />
              </div>
              <div className="flex-1 min-w-[200px]">
                <CustomSelect
                  options={[]}
                  onValueChange={() => {}}
                  placeholder="Select project"
                  label={
                    <span className="flex items-center">
                      {ProjectIcons} Project
                    </span>
                  }
                  disabled
                />
              </div>
              <div className="flex-1 min-w-[200px]">
                <CustomSelect
                  options={[]}
                  onValueChange={() => {}}
                  placeholder="Select family"
                  label={
                    <span className="flex items-center">
                      {FamilyIcons} Family
                    </span>
                  }
                  disabled
                />
              </div>
              <div className="flex-1 min-w-[200px]">
                <CustomSelect
                  options={[]}
                  onValueChange={() => {}}
                  placeholder="Select value stream"
                  label={
                    <span className="flex items-center">
                      {LineChartIcons} Value Stream
                    </span>
                  }
                  disabled
                />
              </div>
              <div className="flex-1 min-w-[200px]">
                <CustomSelect
                  options={[]}
                  onValueChange={() => {}}
                  placeholder="Select area"
                  label={
                    <span className="flex items-center">{AreaIcons} Area</span>
                  }
                  disabled
                />
              </div>
              <div className="flex-1 min-w-[200px]">
                <CustomSelect
                  options={[]}
                  onValueChange={() => {}}
                  placeholder="Select Team"
                  label={
                    <span className="flex items-center">{TeamIcons} Team</span>
                  }
                  disabled
                />
              </div>
            </div>
          </div>
        </div>
        {/* Attendance sheet info row */}
        <div className="flex items-center gap-3 flex-shrink-0 min-w-0 mt-1">
          <div className="p-1 rounded-md flex-shrink-0">
            <CustomIcon
              name="dayView"
              style={{ width: "30px", height: "30px", fill: "#5B7291" }}
            />
          </div>
          <div className="min-w-0">
            <h1 className="text-lg font-medium text-[#5B7291] truncate">
              Attendance sheet Team Leader 4857 - Amine SIDKI -{" "}
              {selectedValidationTeam.teamName}
            </h1>
            <p className="text-sm text-[#8CA2C0] truncate">
              Morning shift (06:00 AM to 14:00 AM)
            </p>
          </div>
          <div className="flex gap-3 ml-auto">
            <Button
              className="flex items-center gap-2 px-3 py-5 rounded-lg border-2 border-green-600 bg-black text-white font-semibold shadow hover:bg-green-900 focus:outline-none"
              style={{ borderColor: "#1DB954" }}
              onClick={handleConfirmSpecialView}
            >
              <svg
                width="20"
                height="20"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                className="text-green-400"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 13l4 4L19 7"
                />
              </svg>
              Confirm
            </Button>
            <Button
              className="flex items-center gap-2 px-3 py-5 rounded-lg border-2 border-red-600 bg-black text-white font-semibold shadow hover:bg-red-900 focus:outline-none"
              style={{ borderColor: "#FF3B30" }}
              onClick={handleOpenSendForReviewDialog}
            >
              <svg
                width="20"
                height="20"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                className="text-red-400"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M15 19l-7-7 7-7"
                />
              </svg>
              Send for review
            </Button>
          </div>
        </div>
        {/* Team Leader Table with checkbox hidden */}
        <div
          className="rounded-lg p-2 flex-grow overflow-x-auto"
          style={{ minHeight: "400px", background: "#fff", border: "none" }}
        >
          <table
            className="w-full border-separate border-spacing-0 table-layout-fixed"
            style={{ borderColor: "#9D9D9D" }}
          >
            <thead className="sticky top-0 bg-white z-10">
              {/* Today indicator row above headers */}
              <th
                className="sticky left-0 z-30 p-1 h-6"
                style={{ backgroundColor: "white" }}
              ></th>
              <th
                className="sticky left-[80px] z-30 p-1 h-6"
                style={{ backgroundColor: "white" }}
              ></th>
              <th
                className="sticky left-[180px] z-30 p-1 h-6"
                style={{ backgroundColor: "white" }}
              ></th>
              <th
                className="sticky left-[280px] z-30 p-1 h-6"
                style={{ backgroundColor: "white" }}
              ></th>
              <th
                className="sticky left-[380px] z-30 p-1 h-6"
                style={{ backgroundColor: "white" }}
              ></th>
              {calendarDays.map(({ day, isToday }) => (
                <th
                  key={`today-${day}`}
                  className="p-1 h-6 min-w-[60px] z-5"
                  style={{ backgroundColor: "white", border: "none" }}
                >
                  {isToday && (
                    <div className="text-xs font-semibold text-[#4762F1] rounded px-1 ">
                      TODAY
                    </div>
                  )}
                </th>
              ))}
            </thead>
            <thead className="border border-b-5 border-[#000000] ">
              <tr>
                <th
                  className="sticky left-0 z-30 border p-2 text-left min-w-[80px] text-sm font-medium"
                  style={{
                    backgroundColor: "#BFD5F1",
                    borderColor: "#9D9D9D",
                    borderBottom: "5px solid #000000",
                  }}
                >
                  Mle
                </th>
                <th
                  className="sticky left-[80px] z-30 border p-2 text-left w-[100px] text-sm font-medium"
                  style={{
                    backgroundColor: "#BFD5F1",
                    borderColor: "#9D9D9D",
                    borderBottom: "5px solid #000000",
                  }}
                >
                  First Name
                </th>
                <th
                  className="sticky left-[180px] z-30 border p-2 text-left w-[100px] text-sm font-medium"
                  style={{
                    backgroundColor: "#BFD5F1",
                    borderColor: "#9D9D9D",
                    borderBottom: "5px solid #000000",
                  }}
                >
                  Last Name
                </th>
                <th
                  className="sticky left-[280px] z-30 border p-2 text-left w-[100px] text-sm font-medium"
                  style={{
                    backgroundColor: "#BFD5F1",
                    borderColor: "#9D9D9D",
                    borderBottom: "5px solid #000000",
                  }}
                >
                  Role
                </th>
                <th
                  className="sticky left-[380px] z-30 border p-2 text-left w-[100px] text-sm font-medium"
                  style={{
                    backgroundColor: "#BFD5F1",
                    borderColor: "#9D9D9D",
                    borderBottom: "5px solid #000000",
                  }}
                >
                  Function
                </th>
                {calendarDays.map(({ day, dayName, isToday }) => (
                  <th
                    key={day}
                    className="border p-1 text-center min-w-[60px] z-5"
                    style={{
                      backgroundColor: isToday ? "#000000" : "#BFD5F1",
                      borderColor: "#9D9D9D",
                      borderBottom: "5px solid #000000",
                      color: isToday ? "#FFFFFF" : "#000000",
                    }}
                  >
                    <div className="text-xs font-medium">
                      {day} {dayName}
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {filteredWorkers.map((worker) => (
                <tr key={worker.id}>
                  <td
                    className="sticky left-0 border p-2 text-sm z-20"
                    style={{
                      backgroundColor: "#F5FBFF",
                      borderColor: "#9D9D9D",
                    }}
                  >
                    {/* Checkbox hidden in special view */}
                    <span>{worker.id}</span>
                  </td>
                  <td
                    className="sticky left-[80px] border p-2 text-sm z-20"
                    style={{
                      backgroundColor: "#F5FBFF",
                      borderColor: "#9D9D9D",
                    }}
                  >
                    {worker.firstName}
                  </td>
                  <td
                    className="sticky left-[180px] border p-2 text-sm z-20"
                    style={{
                      backgroundColor: "#F5FBFF",
                      borderColor: "#9D9D9D",
                    }}
                  >
                    {worker.lastName}
                  </td>
                  <td
                    className="sticky left-[280px] border p-2 text-xs z-20"
                    style={{
                      backgroundColor: "#F5FBFF",
                      borderColor: "#9D9D9D",
                    }}
                  >
                    <div className="text-blue-600 font-medium">
                      {worker.role}
                    </div>
                  </td>
                  <td
                    className="sticky left-[380px] border p-2 text-sm z-20"
                    style={{
                      backgroundColor: "#F5FBFF",
                      borderColor: "#9D9D9D",
                    }}
                  >
                    <div className="text-blue-500 font-medium">
                      {worker.function}
                    </div>
                  </td>
                  {calendarDays.map(({ day, date, isToday }) => {
                    return (
                      <td
                        key={day}
                        className="border p-0 relative z-10"
                        style={{ borderColor: "#9D9D9D" }}
                      >
                        {renderAttendanceCell(worker.id, date, isToday, true)}
                      </td>
                    );
                  })}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <SendForReviewDialog
          isOpen={isSendForReviewDialogOpen}
          onClose={() => {
            setIsSendForReviewDialogOpen(false);
            useAttendanceStore.getState().setSelectedValidationTeam(null);
            setSelectedValidationWorkerId(null);
          }}
          onSubmit={() => {
            if (selectedValidationWorkerId && selectedValidationTeam) {
              updateTeamStatus(
                selectedValidationWorkerId,
                selectedValidationTeam.teamId,
                "Waiting validation...(Reviewed)" as ClockingValidationStatus,
              );
            }
            setIsSendForReviewDialogOpen(false);
            useAttendanceStore.getState().setSelectedValidationTeam(null);
            setSelectedValidationWorkerId(null);
          }}
          teamLeaderId="4857"
          teamLeaderName="Amine SIDKI"
          teamName={safeString(selectedValidationTeam?.teamName)}
          shiftTime="Morning shift (06:00 AM to 14:00 AM)"
        />
      </div>
    );
  }

  return (
    <div ref={containerRef} className="flex-1 overflow-auto pb-9 relative">
      {/* Loading indicator - only for Team Leader */}
      {userRole === UserRole.TEAM_LEADER &&
        (isTeamDataLoading || isStatusMonitoringLoading || isCurrentStatusLoading) && (
          <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50">
            <div className="flex flex-col items-center gap-4">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
              <p className="text-gray-600">Loading team data...</p>
            </div>
          </div>
        )}

      {/* Show table for all roles - Team Leader will only show when team is selected */}
      <div className="min-w-max">
        <table
          className="w-full border-separate border-spacing-0 table-layout-fixed"
          style={{ borderColor: "#9D9D9D" }}
        >
          <thead className="sticky top-0 bg-white z-10 ">
            {/* Today indicator row */}
            <tr>
              <th
                className="sticky left-0 z-30 p-1 h-6"
                style={{ backgroundColor: "white" }}
              ></th>
              <th
                className="sticky left-[80px] z-30 p-1 h-6"
                style={{ backgroundColor: "white" }}
              ></th>
              <th
                className="sticky left-[180px] z-30 p-1 h-6"
                style={{ backgroundColor: "white" }}
              ></th>
              {!(
                userRole === UserRole.DEPARTEMENT_CLERK ||
                userRole === UserRole.DEPARTEMENT_MANAGER ||
                (userRole === UserRole.SHIFT_LEADER &&
                  activeTab === "my-operators")
              ) && (
                <th
                  className="sticky left-[280px] z-30 p-1 h-6"
                  style={{ backgroundColor: "white" }}
                ></th>
              )}
              <th
                className="sticky z-30 p-1 h-6"
                style={{
                  left:
                    userRole === UserRole.DEPARTEMENT_CLERK ||
                    userRole === UserRole.DEPARTEMENT_MANAGER ||
                    (userRole === UserRole.SHIFT_LEADER &&
                      activeTab === "my-operators")
                      ? 280
                      : 380,
                  backgroundColor: "white",
                }}
              ></th>

              {userRole === UserRole.QUALITY_SUPERVISOR && (
                <>
                  <th
                    className="sticky left-[480px] z-30 p-1 h-6"
                    style={{ backgroundColor: "white" }}
                  ></th>
                  <th
                    className="sticky left-[580px] z-30 p-1 h-6"
                    style={{ backgroundColor: "white" }}
                  ></th>
                </>
              )}
              {calendarDays.map(({ day, isToday }) => (
                <th
                  key={`today-${day}`}
                  className="p-1 h-6 min-w-[60px] z-5"
                  style={{
                    backgroundColor: "white",
                    border: "none",
                  }}
                >
                  {isToday && (
                    <div className="text-xs font-semibold text-[#4762F1] rounded px-1 ">
                      TODAY
                    </div>
                  )}

                </th>
              ))}
            </tr>

            {/* Column headers row */}
            <tr>
              <th
                className="sticky left-0 z-30 border p-2 text-left min-w-[80px] text-sm font-medium"
                style={{
                  backgroundColor: "#BFD5F1",
                  borderColor: "#9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                  borderBottom: "5px solid #000000",
                }}
              >
                <div className="flex items-center gap-2">
                  {userRole === UserRole.TEAM_LEADER && isVisualCheckActive && (
                    <Checkbox
                      checked={
                        filteredWorkers.every((worker) =>
                          selectedWorkers.has(worker.mle),
                        ) && filteredWorkersLength > 0
                      }
                      onCheckedChange={handleSelectAll}
                      aria-label="Select all workers"
                    />
                  )}
                  <span>Mle</span>
                </div>
              </th>
              <th
                className="sticky left-[80px] z-30 border p-2 text-left w-[100px] text-sm font-medium"
                style={{
                  backgroundColor: "#BFD5F1",
                  borderColor: "#9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                  borderBottom: "5px solid #000000",
                }}
              >
                First Name
              </th>
              <th
                className="sticky left-[180px] z-30 border p-2 text-left w-[100px] text-sm font-medium"
                style={{
                  backgroundColor: "#BFD5F1",
                  borderColor: "#9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                  borderBottom: "5px solid #000000",
                }}
              >
                Last Name
              </th>
              {!(
                userRole === UserRole.DEPARTEMENT_CLERK ||
                userRole === UserRole.DEPARTEMENT_MANAGER ||
                userRole === UserRole.SHIFT_LEADER
              ) && (
                <th
                  className="sticky left-[280px] z-30 border p-2 text-left w-[100px] text-sm font-medium"
                  style={{
                    backgroundColor: "#BFD5F1",
                    borderColor: "#9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                    borderBottom: "5px solid #000000",
                  }}
                >
                  <div className="text-blue-600 font-medium">Role</div>
                </th>
              )}
              <th
                className="sticky z-30 border p-2 text-left w-[100px] text-sm font-medium"
                style={{
                  left:
                    userRole === UserRole.DEPARTEMENT_CLERK ||
                    userRole === UserRole.DEPARTEMENT_MANAGER ||
                    userRole === UserRole.SHIFT_LEADER
                      ? 280
                      : 380,
                  backgroundColor: "#BFD5F1",
                  borderColor: "#9D9D9D",
                  borderRight: "1px solid #9D9D9D",
                  borderBottom: "5px solid #000000",
                }}
              >
                <div className="text-blue-500 font-medium">Function</div>
              </th>
              {userRole === UserRole.SHIFT_LEADER &&
                activeTab === "my-teamleaders" && (
                  <th
                    className="sticky left-[380px] z-30 border p-2 text-left min-w-[150px] text-sm font-medium"
                    style={{
                      backgroundColor: "#BFD5F1",
                      borderColor: "#9D9D9D",
                      borderRight: "none",
                      borderBottom: "5px solid #000000",
                      boxShadow: "4px 0 0 0 #9D9D9D",
                    }}
                  >
                    Clocking Validation
                  </th>
                )}
              {userRole === UserRole.QUALITY_SUPERVISOR && (
                <>
                  <th
                    className="sticky left-[480px] z-30 border p-2 text-left w-[100px] text-sm font-medium"
                    style={{
                      backgroundColor: "#BFD5F1",
                      borderColor: "#9D9D9D",
                      borderRight: "1px solid #9D9D9D",
                      borderBottom: "5px solid #000000",
                    }}
                  >
                    Team
                  </th>
                  <th
                    className="sticky left-[580px] z-30 border p-2 text-left w-[100px] text-sm font-medium"
                    style={{
                      backgroundColor: "#BFD5F1",
                      borderColor: "#9D9D9D",
                      borderRight: "none",
                      borderBottom: "5px solid #000000",
                      boxShadow: "4px 0 0 0 #9D9D9D",
                    }}
                  >
                    Line
                  </th>
                </>
              )}
              {calendarDays.map(({ day, dayName, isToday, isFutureStatic }) => (
                <th
                  key={day}
                  className="border p-1 text-center min-w-[60px] z-5"
                  style={{
                    backgroundColor: isToday ? "#000000" : isFutureStatic ? "#E8F4FD" : "#BFD5F1",
                    borderColor: "#9D9D9D",
                    borderBottom: "5px solid #000000",
                    color: isToday ? "#FFFFFF" : "#000000",
                    opacity: isFutureStatic ? 0.7 : 1,
                  }}
                >
                  <div className="text-xs font-medium">
                    {day} {dayName}
                    {isFutureStatic && <div className="text-xs text-gray-500"></div>}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {filteredWorkers.map((worker) => (
              <tr key={worker.id}>
                <td
                  className="sticky left-0 border p-2 text-sm z-20"
                  style={{
                    backgroundColor: "#F5FBFF",
                    borderColor: "#9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                    borderBottom: "1px solid #9D9D9D",
                  }}
                >
                  <div className="flex items-center gap-2">
                    {userRole === UserRole.TEAM_LEADER &&
                      isVisualCheckActive && (
                        <Checkbox
                          checked={selectedWorkers.has(worker.mle)}
                          onCheckedChange={(checked) =>
                            handleSelectWorker(worker.mle, checked as boolean)
                          }
                          aria-label={`Select ${worker.firstName} ${worker.lastName}`}
                        />
                      )}
                    <span>{worker.id}</span>
                  </div>
                </td>
                <td
                  className="sticky left-[80px] border p-2 text-sm z-20"
                  style={{
                    backgroundColor: "#F5FBFF",
                    borderColor: "#9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                    borderBottom: "1px solid #9D9D9D",
                  }}
                >
                  {worker.firstName}
                </td>
                <td
                  className="sticky left-[180px] border p-2 text-sm z-20"
                  style={{
                    backgroundColor: "#F5FBFF",
                    borderColor: "#9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                    borderBottom: "1px solid #9D9D9D",
                  }}
                >
                  {worker.lastName}
                </td>
                {!(
                  userRole === UserRole.DEPARTEMENT_CLERK ||
                  userRole === UserRole.DEPARTEMENT_MANAGER ||
                  userRole === UserRole.SHIFT_LEADER
                ) && (
                  <td
                    className="sticky left-[280px] border p-2 text-xs z-20"
                    style={{
                      backgroundColor: "#F5FBFF",
                      borderColor: "#9D9D9D",
                      borderRight: "1px solid #9D9D9D",
                      borderBottom: "1px solid #9D9D9D",
                    }}
                  >
                    <div className="text-blue-600 font-medium">
                      {worker.role}
                    </div>
                  </td>
                )}
                <td
                  className="sticky border p-2 text-sm z-20"
                  style={{
                    left:
                      userRole === UserRole.DEPARTEMENT_CLERK ||
                      userRole === UserRole.DEPARTEMENT_MANAGER ||
                      userRole === UserRole.SHIFT_LEADER
                        ? 280
                        : 380,
                    backgroundColor: "#F5FBFF",
                    borderColor: "#9D9D9D",
                    borderRight: "1px solid #9D9D9D",
                    borderBottom: "1px solid #9D9D9D",
                  }}
                >
                  <div className="text-blue-500 font-medium">
                    {worker.function}
                  </div>
                </td>
                {!(
                  userRole === UserRole.SHIFT_LEADER &&
                  activeTab === "my-operators"
                ) &&
                  userRole === UserRole.SHIFT_LEADER && (
                    <td
                      className="sticky left-[380px] border p-2 text-xs z-20"
                      style={{
                        backgroundColor: "#F5FBFF",
                        borderColor: "#9D9D9D",
                        borderRight: "none",
                        borderBottom: "1px solid #9D9D9D",
                        boxShadow: "4px 0 0 0 #9D9D9D",
                      }}
                    >
                      <div
                        className={`font-medium ${getClockingValidationColor((worker.clockingValidation as ClockingValidationStatus) || "")}`}
                        onClick={() => {
                          if (
                            worker.clockingValidation ===
                            "Waiting validation..."
                          ) {
                            const team: TeamStatus = {
                              teamId: worker.team || "",
                              teamName: worker.team || "",
                              status:
                                worker.clockingValidation as ClockingValidationStatus,
                              workerCount: 0,
                              validatedCount: 0,
                              waitingCount: 0,
                              absentCount: 0,
                              notFinishedCount: 0,
                            };
                            handleClockingValidationCellClick(team, worker.mle);
                          }
                        }}
                        style={{
                          cursor:
                            worker.clockingValidation ===
                            "Waiting validation..."
                              ? "pointer"
                              : "default",
                        }}
                      >
                        {worker.clockingValidation || "Not finished yet"}
                      </div>
                    </td>
                  )}
                {userRole === UserRole.QUALITY_SUPERVISOR && (
                  <>
                    <td
                      className="sticky left-[480px] border p-2 text-xs z-20"
                      style={{
                        backgroundColor: "#F5FBFF",
                        borderColor: "#9D9D9D",
                        borderRight: "1px solid #9D9D9D",
                        borderBottom: "1px solid #9D9D9D",
                      }}
                    >
                      {worker.team}
                    </td>
                    <td
                      className="sticky left-[580px] border p-2 text-xs z-20"
                      style={{
                        backgroundColor: "#F5FBFF",
                        borderColor: "#9D9D9D",
                        borderRight: "none",
                        borderBottom: "1px solid #9D9D9D",
                        boxShadow: "4px 0 0 0 #9D9D9D",
                      }}
                    >
                      {worker.line}
                    </td>
                  </>
                )}
                {calendarDays.map(({ day, date, isToday, isFutureStatic }) => (
                  <td
                    key={day}
                    className="border p-0 relative z-10"
                    style={{ borderColor: "#9D9D9D" }}
                  >
                    {renderAttendanceCell(worker.id, date, isToday, false)}
                  </td>
                ))}
              </tr>
            ))}

            {/* No Results Message */}
            {filteredWorkers.length === 0 && (
              <tr>
                <td
                  colSpan={
                    calendarDays.length +
                    (userRole === UserRole.DEPARTEMENT_CLERK ||
                    userRole === UserRole.DEPARTEMENT_MANAGER ||
                    userRole === UserRole.SHIFT_LEADER
                      ? 3
                      : 4) +
                    (userRole === UserRole.SHIFT_LEADER &&
                    activeTab !== "my-operators"
                      ? 1
                      : 0) +
                    (userRole === UserRole.QUALITY_SUPERVISOR ? 2 : 0)
                  }
                  className="border p-8 text-center"
                  style={{ borderColor: "#9D9D9D" }}
                >
                  <div className="flex flex-col items-center gap-4">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                      <svg
                        className="w-8 h-8 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                        />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        No Results Found
                      </h3>
                      <p className="text-gray-600">
                        {userRole === UserRole.TEAM_LEADER
                          ? "Please select a team from the filters above to view attendance data."
                          : "No workers found matching the current criteria."}
                      </p>
                    </div>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      {statusMenuInfo?.isOpen && (
        <div
          style={{
            position: "absolute",
            left: statusMenuInfo.position.x,
            top: statusMenuInfo.position.y,
            zIndex: 1000,
          }}
        >
          <MyTeamStatusMenue
            position={{ x: 0, y: 0 }}
            onSelect={handleStatusSelect}
            onClose={closeStatusMenu}
            onOpenRequestDialog={handleOpenRequestDialog}
            onOpenAttendanceSheetDialog={handleOpenAttendanceSheetDialog}
            onOpenReplaceDialog={handleOpenReplaceDialog}
            replace={statusMenuInfo?.replace}
          />
        </div>
      )}
      {/* Request Dialog */}
      <RequestDialog
        isOpen={isRequestDialogOpen}
        onClose={handleCloseRequestDialog}
        onNext={handleOpenRequestForm}
        userInfo={{
          name: "Ahmed Malik",
          id: "123953",
          status: "Present (Line)",
        }}
      />
      {/* Request Form Dialog */}
      <RequestFormDialog
        isOpen={isRequestFormDialogOpen}
        onClose={handleCloseRequestFormDialog}
        onBack={handleBackToRequestList}
        requestType={selectedRequestType}
        userInfo={{
          name: "Ahmed Malik",
          id: "123953",
          status: "Present (Line)",
        }}
      />
      {/* <AttendanceSheetDialog
        isOpen={attendanceDialogInfo.isOpen}
        onClose={handleCloseAttendanceSheetDialog}
        worker={attendanceDialogInfo.worker || { id: "", name: "" }}
        date={attendanceDialogInfo.date || new Date()}
        userInfo={{
          name: "Ahmed Malik",
          id: "123953",
          status: "Present (Line)",
        }}
      /> */}
      {attendanceDialogInfo.isOpen && (
        <DetailsDialog
          isOpen={attendanceDialogInfo.isOpen}
          onClose={handleCloseAttendanceSheetDialog}
          worker={attendanceDialogInfo.worker || { id: "", name: "" }}
          date={attendanceDialogInfo.date || new Date()}
        />
      )}
      {/* ClockingValidationDialog */}
      <ClockingValidationDialog
        isOpen={isClockingValidationDialogOpen}
        onClose={handleCloseClockingValidationDialog}
        workerId={selectedWorkerId}
      />
      {isLoadingOperator && isReplaceDialogOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-8 flex flex-col items-center gap-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            <p className="text-gray-600">Loading operator data...</p>
          </div>
        </div>
      )}

      {operatorError && isReplaceDialogOpen && !isLoadingOperator && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-8 flex flex-col items-center gap-4 text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
              <CustomIcon
                name="error"
                style={{ width: "24px", height: "24px", fill: "#DC2626" }}
              />
            </div>
            <div>
              <p className="text-red-600 font-semibold mb-2">
                Failed to load operator data
              </p>
              <p className="text-gray-600">{operatorError}</p>
            </div>
            <div className="flex gap-3">
              <Button
                onClick={() => {
                  setIsReplaceDialogOpen(false);
                  setReplaceWorkerMle(null);
                  setAbsentEmployeeOperator(undefined);
                  setOperatorError(null);
                }}
                variant="outline"
              >
                Close
              </Button>
              <Button
                onClick={() => window.location.reload()}
                className="bg-blue-600 text-white hover:bg-blue-700"
              >
                Retry
              </Button>
            </div>
          </div>
        </div>
      )}

      {!isLoadingOperator && !operatorError && (
        <ReplaceDialog
          isOpen={isReplaceDialogOpen}
          onClose={() => {
            setIsReplaceDialogOpen(false);
            setReplaceWorkerMle(null);
            setAbsentEmployeeOperator(undefined);
            setOperatorError(null);
          }}
          absentEmployee={absentEmployeeOperator}
          teamLeaderId="3e2bac24-5866-4065-8a7b-914c2e077cf1"
          shiftId="b4bedff2-165e-4156-969f-d3b3cd025970"
        />
      )}
    </div>
  );
}
