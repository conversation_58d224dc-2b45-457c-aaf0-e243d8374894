"use client";

import { useState, useMemo, useEffect } from "react";

import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { ReusableDialog } from "@/components/common/CustomDialog";
import {
  formatDateToYYYYMMDD,
  adjustOverlappingTimeSlots,
  parseTime,
  STATUS_MAP,
  calculateDurationInMinutes,
} from "../../types/Types";
import { AddTimeSlotForm } from "../AddTimeSlots";
import { TimeSlotSummary } from "../TimeSlotSummary";
import { TimeSlotListItem } from "../TimeSlotItem";
import { TimelineView } from "../TimeLineView";
import { useTeamLeaderStore } from "../../store/teamLeaderStore";

interface TimeSlot {
  id: string;
  date: string; // YYYY-MM-DD
  start: string; // HH:MM
  end: string; // HH:MM
  status: string;
  duration: string;
}

interface DetailsDialogProps {
  isOpen?: boolean;
  onClose?: () => void;
  worker?: { id: string; name: string; mle?: string };
  date?: Date;
  operatorId?: string;
  shiftInstantId?: string;
}

export function DetailsDialog({
  isOpen: externalIsOpen = true,
  onClose: externalOnClose,
  date: externalDate,
  worker,
  operatorId,
  shiftInstantId,
}: DetailsDialogProps = {}) {
  const [isOpen, setIsOpen] = useState(externalIsOpen);
  const [selectedDate, setSelectedDate] = useState<Date>(
    externalDate || new Date(),
  );

  // API integration for operator details
  const {
    operatorDetails,
    isOperatorDetailsLoading,
    operatorDetailsError,
    fetchOperatorDetails,
  } = useTeamLeaderStore();

  const operatorData = operatorId ? operatorDetails[operatorId] : null;

  // Sync external props with internal state
  useEffect(() => {
    setIsOpen(externalIsOpen);
  }, [externalIsOpen]);

  useEffect(() => {
    if (externalDate) {
      setSelectedDate(externalDate);
    }
  }, [externalDate]);

  // Fetch operator details when dialog opens
  useEffect(() => {
    if (isOpen && operatorId && shiftInstantId && !operatorData) {
      fetchOperatorDetails(operatorId, shiftInstantId);
    }
  }, [isOpen, operatorId, shiftInstantId, operatorData, fetchOperatorDetails]);

  const handleClose = () => {
    setIsOpen(false);
    if (externalOnClose) {
      externalOnClose();
    }
  };

  // Helper functions for API data
  const formatTime = (isoString: string) => {
    const date = new Date(isoString);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  const formatDate = (isoString: string) => {
    const date = new Date(isoString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (statusCode: string) => {
    switch (statusCode) {
      case "P":
        return "bg-green-100 text-green-800";
      case "P_IN_BUS":
        return "bg-blue-100 text-blue-800";
      case "P_IN_PLANT":
        return "bg-green-200 text-green-900";
      case "AB":
        return "bg-red-100 text-red-800";
      case "CTN":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([
    {
      id: "1",
      date: formatDateToYYYYMMDD(new Date()),
      start: "09:00",
      end: "09:30", // 30 minutes
      status: "F",
      duration: "30min",
    },
    {
      id: "2",
      date: formatDateToYYYYMMDD(new Date()),
      start: "09:30",
      end: "09:45", // 15 minutes
      status: "P",
      duration: "15min",
    },
    {
      id: "3",
      date: formatDateToYYYYMMDD(new Date()),
      start: "09:45",
      end: "10:30", // 45 minutes
      status: "P in lan",
      duration: "45min",
    },
    {
      id: "4",
      date: formatDateToYYYYMMDD(new Date()),
      start: "10:30",
      end: "12:00", // 90 minutes
      status: "CTP",
      duration: "1h 30min",
    },
    {
      id: "5",
      date: formatDateToYYYYMMDD(new Date()),
      start: "13:00",
      end: "15:30", // 150 minutes
      status: "P",
      duration: "2h 30min",
    },
    {
      id: "6",
      date: formatDateToYYYYMMDD(new Date()),
      start: "15:30",
      end: "16:00", // 30 minutes
      status: "DN",
      duration: "30min",
    },
    // Add some slots for a different day to demonstrate filtering
    {
      id: "7",
      date: formatDateToYYYYMMDD(
        new Date(new Date().setDate(new Date().getDate() + 1)),
      ), // Tomorrow
      start: "08:00",
      end: "09:00",
      status: "CR",
      duration: "1h",
    },
  ]);

  const legendItems = Object.entries(STATUS_MAP).filter(
    ([key]) => key !== "Active",
  ); // Exclude 'Active' from main legend

  const handleAddTimeSlot = (
    start: string,
    end: string,
    status: string,
    duration: string,
  ) => {
    const newSlot: TimeSlot = {
      id: String(Date.now()), // Simple unique ID
      date: formatDateToYYYYMMDD(selectedDate), // Assign the currently selected date
      start,
      end,
      status,
      duration,
    };

    setTimeSlots((prev) => {
      // Adjust overlapping time slots
      const adjustedSlots = adjustOverlappingTimeSlots(prev, {
        start: newSlot.start,
        end: newSlot.end,
        date: newSlot.date,
      });

      // Add the new slot and sort by date then by start time
      const updatedSlots = [...adjustedSlots, newSlot].sort((a, b) => {
        const dateA = new Date(a.date);
        const dateB = new Date(b.date);
        if (dateA.getTime() !== dateB.getTime()) {
          return dateA.getTime() - dateB.getTime();
        }
        return parseTime(a.start) - parseTime(b.start);
      });

      return updatedSlots;
    });
  };

  const handleRemoveTimeSlot = (id: string) => {
    setTimeSlots((prev) => prev.filter((slot) => slot.id !== id));
  };

  const filteredTimeSlots = useMemo(() => {
    const selectedDateString = formatDateToYYYYMMDD(selectedDate);
    return timeSlots.filter((slot) => slot.date === selectedDateString);
  }, [timeSlots, selectedDate]);

  const minStartTimeForNewSlot = useMemo(() => {
    if (filteredTimeSlots.length === 0) {
      // If no slots for the day, allow starting from 00:00 of the selected date
      const startOfDay = new Date(selectedDate);
      startOfDay.setHours(0, 0, 0, 0);
      return startOfDay;
    }
    // Allow starting from any time (overlaps will be handled automatically)
    const startOfDay = new Date(selectedDate);
    startOfDay.setHours(0, 0, 0, 0);
    return startOfDay;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedDate]);

  const timeSlotSummaryData = useMemo(() => {
    const summary: {
      [key: string]: {
        totalDurationMinutes: number;
        count: number;
        color: string;
        name: string;
      };
    } = {};

    filteredTimeSlots.forEach((slot) => {
      const statusInfo = STATUS_MAP[slot.status] || {
        name: slot.status,
        color: "bg-gray-400",
      };
      const durationMinutes = calculateDurationInMinutes(slot.start, slot.end);

      if (!summary[slot.status]) {
        summary[slot.status] = {
          totalDurationMinutes: 0,
          count: 0,
          color: statusInfo.color,
          name: statusInfo.name,
        };
      }
      summary[slot.status].totalDurationMinutes += durationMinutes;
      summary[slot.status].count += 1;
    });

    return Object.values(summary).map((item) => ({
      status: item.name,
      totalDurationMinutes: item.totalDurationMinutes,
      count: item.count,
      color: item.color,
    }));
  }, [filteredTimeSlots]);

  const footer = (
    <div className="flex justify-end gap-2">
      <Button variant="outline" onClick={handleClose}>
        Cancel
      </Button>
      <Button type="submit">Submit</Button>
    </div>
  );

  return (
    <ReusableDialog
      isOpen={isOpen}
      onClose={handleClose}
      title={operatorData ? `Operator Details - ${worker?.name || 'Unknown'}` : "Attendance sheet"}
      size="5xl"
      footer={footer}
    >
      <div className="grid gap-6 py-4">
        {/* Show loading state for API data */}
        {isOperatorDetailsLoading && operatorId && (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2">Loading operator details...</span>
          </div>
        )}

        {/* Show error state for API data */}
        {operatorDetailsError && operatorId && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-red-800">Error: {operatorDetailsError}</p>
          </div>
        )}

        {/* Show API data when available */}
        {operatorData && !isOperatorDetailsLoading && (
          <>
            {/* Current Status */}
            <div>
              <h3 className="text-lg font-semibold mb-3">Current Status</h3>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center gap-3">
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(operatorData.current_status.code)}`}>
                    {operatorData.current_status.code}
                  </span>
                  <span className="text-gray-700">{operatorData.current_status.description}</span>
                  <span className="text-sm text-gray-500">
                    Since {formatTime(operatorData.current_status.activatedAt)} on {formatDate(operatorData.current_status.activatedAt)}
                  </span>
                </div>
              </div>
            </div>

            <Separator />

            {/* Status Time Summary */}
            <div>
              <h3 className="text-lg font-semibold mb-3">Time Summary</h3>
              <div className="grid gap-3">
                {operatorData.status_time_summary.map((summary, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(summary.status_code)}`}>
                        {summary.status_code}
                      </span>
                      <span className="text-gray-700">{summary.status_description}</span>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold">{summary.total_time_formatted}</div>
                      <div className="text-sm text-gray-500">{summary.total_time_minutes} minutes</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <Separator />

            {/* Status History */}
            <div>
              <h3 className="text-lg font-semibold mb-3">Status History</h3>
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {operatorData.status_history.map((status, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(status.code)}`}>
                        {status.code}
                      </span>
                      <span className="text-gray-700">{status.description}</span>
                    </div>
                    <div className="text-right text-sm text-gray-500">
                      <div>From: {formatTime(status.activatedAt)} on {formatDate(status.activatedAt)}</div>
                      {status.endedAt && (
                        <div>To: {formatTime(status.endedAt)} on {formatDate(status.endedAt)}</div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}

        {/* Show existing time slots UI when no API data */}
        {!operatorData && !isOperatorDetailsLoading && (
          <>
            <div>
              <h2 className="mb-3 text-lg font-semibold">Timeline View</h2>
              <TimelineView timeSlots={filteredTimeSlots} />
            </div>

            <div>
              <h2 className="mb-3 text-lg font-semibold">
                Time Slots ({timeSlotSummaryData.length})
              </h2>
              <TimeSlotSummary summaryData={timeSlotSummaryData} />
            </div>

            <Separator />

            <div>
              <h2 className="mb-3 text-lg font-semibold">Add time slot</h2>
              <AddTimeSlotForm
                onAddTimeSlot={handleAddTimeSlot}
                selectedDate={selectedDate}
                minStartTime={minStartTimeForNewSlot}
              />
            </div>

            <div>
              <h2 className="mb-3 text-lg font-semibold">
                Time Slots ({filteredTimeSlots.length})
              </h2>
              <div className="grid gap-3">
                {timeSlots.length === 0 ? (
                  <p className="text-center text-muted-foreground">
                    No time slots added for this date yet.
                  </p>
                ) : (
                  timeSlots.map((slot) => (
                    <TimeSlotListItem
                      key={slot.id}
                      id={slot.id}
                      start={slot.start}
                      end={slot.end}
                      duration={slot.duration}
                      status={slot.status}
                      onRemove={handleRemoveTimeSlot}
                    />
                  ))
                )}
              </div>
            </div>

            <Separator />

            <div>
              <h2 className="mb-3 text-lg font-semibold">Legends</h2>
              <div className="grid grid-cols-2 gap-x-6 gap-y-2 text-sm sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
                {legendItems.map(([key, value]) => (
                  <div key={key} className="flex items-center gap-2">
                    <div
                      className={`size-3 shrink-0 rounded-sm ${value.color}`}
                      aria-hidden="true"
                    />
                    <span>
                      <span className="font-medium">{key}</span>:{" "}
                      <span className="text-xs text-muted-foreground">
                        {value.name}
                      </span>
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}
      </div>
    </ReusableDialog>
  );
}
