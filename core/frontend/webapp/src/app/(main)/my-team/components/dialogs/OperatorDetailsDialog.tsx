"use client";

import { useEffect } from "react";
import { ReusableDialog } from "@/components/common/CustomDialog";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { useTeamLeaderStore } from "../../store/teamLeaderStore";

interface OperatorDetailsDialogProps {
  isOpen: boolean;
  shiftInstantId: string;
  onClose: () => void;
  operatorId: string;
  operatorName: string;
}

export function OperatorDetailsDialog({
  isOpen,
  onClose,
  operatorId,
  shiftInstantId,
  operatorName,
}: OperatorDetailsDialogProps) {
  const {
    operatorDetails,
    isOperatorDetailsLoading,
    operatorDetailsError,
    fetchOperatorDetails,
  } = useTeamLeaderStore();

  const operatorData = operatorDetails[operatorId];

  useEffect(() => {
    if (isOpen && operatorId && !operatorData) {
      fetchOperatorDetails(operatorId, shiftInstantId);
    }
  }, [isOpen, operatorId, operatorData, fetchOperatorDetails]);

  const formatTime = (isoString: string) => {
    const date = new Date(isoString);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  const formatDate = (isoString: string) => {
    const date = new Date(isoString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (statusCode: string) => {
    switch (statusCode) {
      case "P":
        return "bg-green-100 text-green-800";
      case "P_IN_BUS":
        return "bg-blue-100 text-blue-800";
      case "P_IN_PLANT":
        return "bg-green-200 text-green-900";
      case "AB":
        return "bg-red-100 text-red-800";
      case "CTN":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const footer = (
    <div className="flex justify-end gap-2">
      <Button variant="outline" onClick={onClose}>
        Close
      </Button>
    </div>
  );

  return (
    <ReusableDialog
      isOpen={isOpen}
      onClose={onClose}
      title={`Operator Details - ${operatorName}`}
      size="4xl"
      footer={footer}
    >
      <div className="grid gap-6 py-4">
        {isOperatorDetailsLoading && (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2">Loading operator details...</span>
          </div>
        )}

        {operatorDetailsError && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-red-800">Error: {operatorDetailsError}</p>
          </div>
        )}

        {operatorData && (
          <>
            {/* Current Status */}
            <div>
              <h3 className="text-lg font-semibold mb-3">Current Status</h3>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center gap-3">
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(operatorData.current_status.code)}`}>
                    {operatorData.current_status.code}
                  </span>
                  <span className="text-gray-700">{operatorData.current_status.description}</span>
                  <span className="text-sm text-gray-500">
                    Since {formatTime(operatorData.current_status.activatedAt)} on {formatDate(operatorData.current_status.activatedAt)}
                  </span>
                </div>
              </div>
            </div>

            <Separator />

            {/* Status Time Summary */}
            <div>
              <h3 className="text-lg font-semibold mb-3">Time Summary</h3>
              <div className="grid gap-3">
                {operatorData.status_time_summary.map((summary, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(summary.status_code)}`}>
                        {summary.status_code}
                      </span>
                      <span className="text-gray-700">{summary.status_description}</span>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold">{summary.total_time_formatted}</div>
                      <div className="text-sm text-gray-500">{summary.total_time_minutes} minutes</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <Separator />

            {/* Status History */}
            <div>
              <h3 className="text-lg font-semibold mb-3">Status History</h3>
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {operatorData.status_history.map((status, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(status.code)}`}>
                        {status.code}
                      </span>
                      <span className="text-gray-700">{status.description}</span>
                    </div>
                    <div className="text-right text-sm text-gray-500">
                      <div>From: {formatTime(status.activatedAt)} on {formatDate(status.activatedAt)}</div>
                      {status.endedAt && (
                        <div>To: {formatTime(status.endedAt)} on {formatDate(status.endedAt)}</div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Scheduled Status (if any) */}
            {operatorData.scheduled_status.length > 0 && (
              <>
                <Separator />
                <div>
                  <h3 className="text-lg font-semibold mb-3">Scheduled Status</h3>
                  <div className="space-y-3">
                    {operatorData.scheduled_status.map((status, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <div className="flex items-center gap-3">
                          <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(status.code)}`}>
                            {status.code}
                          </span>
                          <span className="text-gray-700">{status.description}</span>
                        </div>
                        <div className="text-right text-sm text-blue-600">
                          <div>Scheduled: {formatTime(status.activatedAt)} on {formatDate(status.activatedAt)}</div>
                          {status.endedAt && (
                            <div>Until: {formatTime(status.endedAt)} on {formatDate(status.endedAt)}</div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </>
            )}
          </>
        )}
      </div>
    </ReusableDialog>
  );
}
