"use client";

import { useState, useRef } from "react";
import { ReusableDialog } from "@/components/common/CustomDialog";
import { Button } from "@/components/ui/button";
import { MyTeamStatusMenue } from "../MyTeamStatusMenue";
import { DatePicker } from "@/components/ui/datePicker";
import {
  useAttendanceStore,
  AttendanceHistoryEntry,
} from "../../store/myTeamStore";

interface AttendanceSheetDialogProps {
  isOpen: boolean;
  onClose: () => void;
  worker: {
    id: string;
    name: string;
  };
  date: Date;
  userInfo?: {
    name: string;
    id?: string;
    status?: string;
  };
}

const HOURS = Array.from({ length: 24 }, (_, i) => i);

export default function AttendanceSheetDialog({
  isOpen,
  onClose,
  date,
  userInfo,
  worker,
}: AttendanceSheetDialogProps) {
  const attendanceSheetDetails = useAttendanceStore(
    (s) => s.attendanceSheetDetails,
  );
  // Example: shift from 6 to 14, present from 6 to 14 except 12-13 is AB
  const dateStr = date.toISOString().split("T")[0];
  const key = `${worker?.id || ""}_${dateStr}`;
  const initialSequence = attendanceSheetDetails[key];
  const [presence, setPresence] = useState<{ [hour: number]: string }>(() => {
    const obj: { [hour: number]: string } = {};
    if (initialSequence && initialSequence.length === 8) {
      for (let i = 6; i < 14; i++) obj[i] = initialSequence[i - 6];
    } else {
      for (let i = 6; i < 14; i++) obj[i] = "P";
    }
    return obj;
  });
  const [menuHour, setMenuHour] = useState<number | null>(null);
  const [menuPos, setMenuPos] = useState<{ x: number; y: number } | null>(null);
  const [showHistory, setShowHistory] = useState(false);
  const [history, setHistory] = useState<AttendanceHistoryEntry[]>([]);
  const [updatedHours, setUpdatedHours] = useState<Set<number>>(new Set());
  const containerRef = useRef<HTMLDivElement>(null);
  const submitAttendanceSheet = useAttendanceStore(
    (s) => s.submitAttendanceSheet,
  );
  const getAttendanceHistory = useAttendanceStore(
    (s) => s.getAttendanceHistory,
  );
  const setAttendanceSheetDetails = useAttendanceStore(
    (s) => s.setAttendanceSheetDetails,
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case "P":
        return "bg-[#4CAF501C] text-[#4CAF50] hover:bg-green-100 hover:text-[#4CAF50]";
      case "CTN":
        return "bg-[#FBE3E3] text-[#D60000] hover:bg-red-200 hover:text-[#D60000]";
      case "AB":
        return "bg-[#FBE3E3] text-[#D60000] hover:bg-red-200 hover:text-[#D60000]";
      default:
        return "bg-white hover:bg-gray-50";
    }
  };

  const getHistoryStatusColor = (status: string) => {
    switch (status) {
      case "P":
        return "bg-gray-100 text-[#4CAF50]";
      case "CTN":
        return "bg-gray-100 text-[#D60000]";
      case "AB":
        return "bg-gray-100 text-[#D60000]";
      default:
        return "bg-gray-100 text-gray-400";
    }
  };

  const handleCellClick = (hour: number, e: React.MouseEvent) => {
    const currentStatus = presence[hour] || "P";

    if (currentStatus === "P") {
      // Only show menu for "P" status
      const cellRect = (e.currentTarget as HTMLElement).getBoundingClientRect();
      const containerRect = containerRef.current?.getBoundingClientRect();
      let x = cellRect.left;
      let y = cellRect.bottom;

      if (containerRect && containerRef.current) {
        const MENU_OFFSET_X = 0; // px, small gap to the left
        const MENU_OFFSET_Y = 0; // px, move menu up a bit
        x =
          cellRect.left -
          containerRect.left +
          containerRef.current.scrollLeft +
          cellRect.width +
          MENU_OFFSET_X;
        y =
          cellRect.bottom -
          containerRect.top +
          containerRef.current.scrollTop +
          MENU_OFFSET_Y;
      }

      setMenuHour(hour);
      setMenuPos({ x, y });
    } else {
      // If not "P", set it back to "P"
      setPresence((prev) => ({ ...prev, [hour]: "P" }));
      setUpdatedHours((prev) => new Set(prev).add(hour));
    }
  };

  const handleStatusSelect = (status: string) => {
    if (menuHour !== null) {
      setPresence((prev) => ({ ...prev, [menuHour]: status }));
      setUpdatedHours((prev) => new Set(prev).add(menuHour));
    }
    setMenuHour(null);
    setMenuPos(null);
  };

  const handleRaiseRequest = () => {
    // Implement raise request logic for this hour
    setMenuHour(null);
    setMenuPos(null);
  };

  const handleCancel = () => {
    // Reset all state to initial values
    setPresence(() => {
      const obj: { [hour: number]: string } = {};
      for (let i = 6; i < 14; i++) obj[i] = "P";
      return obj;
    });
    setMenuHour(null);
    setMenuPos(null);
    setShowHistory(false);
    setHistory([]);
    setUpdatedHours(new Set());
    onClose();
  };

  const handleSubmit = () => {
    if (!worker) return;
    const dateStr = date.toISOString().split("T")[0];

    if (!showHistory) {
      // First submit: show history
      submitAttendanceSheet(
        worker.id,
        dateStr,
        presence,
        userInfo?.name || "Unknown User",
      );
      const attendanceHistory = getAttendanceHistory(worker.id, dateStr);
      setHistory(attendanceHistory);
      setShowHistory(true);
    } else {
      // Second submit: save sequence to store and close dialog
      const sequence: string[] = [];
      for (let h = 6; h < 14; h++) {
        sequence.push(presence[h] || "P");
      }
      setAttendanceSheetDetails(worker.id, dateStr, sequence);
      setShowHistory(false);
      setHistory([]);
      setUpdatedHours(new Set());
      onClose();
    }
  };

  const renderHistoryTable = () => {
    if (!showHistory) return null;

    // Function to get changed hours for a history entry
    const getChangedHours = (entry: AttendanceHistoryEntry, index: number) => {
      // Only show underlines for the first (most recent) entry, using the hours that were updated
      if (index === 0) {
        return updatedHours;
      }
      return new Set<number>();
    };

    return (
      <div className="mt-8 border-t pt-6">
        <h2 className="text-lg font-semibold mb-1">History</h2>
        <p className="text-sm text-gray-500 mb-4">13 Dec 2024 - 08:47</p>
        <div className="space-y-6">
          {history.map((entry, index) => {
            const changedHours = getChangedHours(entry, index);
            return (
              <div key={entry.id} className="p-4">
                {/* <div className="flex justify-between items-center mb-4">
                <div className="flex items-center gap-4">
                  <span className="text-sm text-gray-700">
                    Updated by: {entry.submittedBy}
                  </span>
                </div>
              </div> */}
                <div className="overflow-x-auto">
                  <div className="flex flex-row items-end">
                    {HOURS.map((h) => (
                      <div
                        key={h}
                        className="flex flex-col items-center"
                        style={{ minWidth: 60 }}
                      >
                        <div className="text-xs text-gray-400 border border-gray-300 w-full text-center">
                          {h.toString().padStart(2, "0")}:00
                          <br />
                          {((h + 1) % 24).toString().padStart(2, "0")}:00
                        </div>
                        <div
                          className={`w-full h-8 flex items-center justify-center border border-gray-300 ${
                            h >= 6 && h < 14
                              ? getHistoryStatusColor(
                                  entry.attendanceData[h] || "P",
                                )
                              : "bg-gray-100"
                          }`}
                          style={{
                            borderLeft:
                              h === 6 || h === 14 || h === 22
                                ? "2px solid #ccc"
                                : undefined,
                          }}
                        >
                          {h >= 6 && h < 14 ? (
                            <div className="relative">
                              <span className="text-sm font-semibold">
                                {entry.attendanceData[h] || "P"}
                              </span>
                              {changedHours.has(h) && (
                                <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gray-600"></div>
                              )}
                            </div>
                          ) : null}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
        {/* Updated hours indicator */}
        <div className="flex items-center gap-2 mt-4">
          <div className="w-4 h-0.5 bg-gray-600"></div>
          <span className="text-sm text-gray-600">:Updated hours</span>
        </div>
      </div>
    );
  };

  return (
    <ReusableDialog
      isOpen={isOpen}
      onClose={onClose}
      title="Attendance sheet"
      size="7xl"
    >
      <div
        ref={containerRef}
        className="w-full max-w-6xl mx-auto min-h-0 flex flex-col p-4 relative"
      >
        <div className="flex flex-row justify-between items-center mb-6">
          <div>
            <span className="font-semibold">Worker:</span>
            <div className="text-lg">{userInfo?.name}</div>
          </div>
          <div>
            <span className="font-semibold">Worked Hours</span>
            <div className="text-lg">
              {Object.values(presence).filter((v) => v === "P").length} Hours
            </div>
          </div>
          <div>
            <DatePicker
              label="Date (Today):"
              name="attendance-date"
              defaultDate={date}
              className="w-48"
              dateFormat="dd/MM/yyyy"
              onChange={() => {
                // Handle date change if needed
              }}
              placeholder="Select a date"
            />
          </div>
        </div>

        {/* Current Attendance Sheet Section */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold mb-1">
            Current Attendance Sheet
          </h2>
          <p className="text-sm text-gray-500 mb-4">
            {new Date().toLocaleDateString("en-GB", {
              day: "2-digit",
              month: "short",
              year: "numeric",
            })}{" "}
            -{" "}
            {new Date().toLocaleTimeString("en-GB", {
              hour: "2-digit",
              minute: "2-digit",
            })}
          </p>
          <div className="overflow-x-auto">
            <div className="flex flex-row items-end">
              {HOURS.map((h) => (
                <div
                  key={h}
                  className="flex flex-col items-center"
                  style={{ minWidth: 60 }}
                >
                  <div className="text-xs text-gray-500 border border-gray-300 w-full text-center">
                    {h.toString().padStart(2, "0")}:00
                    <br />
                    {((h + 1) % 24).toString().padStart(2, "0")}:00
                  </div>
                  <div
                    className={`w-full h-8 flex items-center justify-center border border-gray-300 cursor-pointer ${h >= 6 && h < 14 ? getStatusColor(presence[h] || "P") : "bg-white"}`}
                    onClick={
                      h >= 6 && h < 14
                        ? (e) => handleCellClick(h, e)
                        : undefined
                    }
                    style={{
                      borderLeft:
                        h === 6 || h === 14 || h === 22
                          ? "2px solid black"
                          : undefined,
                    }}
                  >
                    {h >= 6 && h < 14 ? (
                      <div className="relative">
                        <span className="text-sm font-semibold">
                          {presence[h] || "P"}
                        </span>
                        {showHistory && updatedHours.has(h) && (
                          <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-black"></div>
                        )}
                      </div>
                    ) : null}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
        {renderHistoryTable()}
        <div className="flex justify-between flex-shrink-0 pt-8">
          <Button
            variant="outline"
            onClick={showHistory ? handleCancel : onClose}
            className="px-6 py-2"
          >
            {showHistory ? "Cancel" : "Back"}
          </Button>
          <div className="flex gap-3">
            {showHistory && (
              <Button
                variant="outline"
                onClick={() => {}}
                className="px-6 py-2"
              >
                Export
              </Button>
            )}
            <Button
              onClick={handleSubmit}
              className="px-6 py-2 bg-black text-white hover:bg-gray-800"
            >
              Submit
            </Button>
          </div>
        </div>

        {menuHour !== null && menuPos && (
          <div
            style={{
              position: "absolute",
              left: menuPos.x,
              top: menuPos.y,
              zIndex: 1000,
            }}
          >
            <MyTeamStatusMenue
              position={{ x: 0, y: 0 }}
              onSelect={handleStatusSelect}
              onClose={() => {
                setMenuHour(null);
                setMenuPos(null);
              }}
              onOpenRequestDialog={handleRaiseRequest}
              showMoreDetails={false}
            />
          </div>
        )}
      </div>
    </ReusableDialog>
  );
}
