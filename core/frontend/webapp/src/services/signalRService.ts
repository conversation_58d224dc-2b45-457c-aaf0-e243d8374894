import { Notification, Alert } from "@/types/notification.types";
import { API_URL } from "@/lib/axios";
import type { HubConnection } from "@microsoft/signalr";

// Status monitoring types
export interface OperatorStatusUpdate {
  operatorId: number;
  teamId: string;
  statusCode: string;
  timestamp: string;
  shiftInstantId?: string;
}

export interface ShiftInstantStatusUpdate {
  shiftInstantId: string;
  previousStatus: string;
  currentStatus: string;
  timestamp: string;
  updatedAt?: string;
}

export interface TeamStatusMonitoringUpdate {
  teamId: string;
  updatedAt: string;
  shiftDate?: string;
}

export interface SignalRService {
  connect: (userId: string) => Promise<void>;
  disconnect: () => void;
  onNotification: (callback: (notification: Notification) => void) => void;
  onAlert: (callback: (alert: Alert) => void) => void;
  isConnected: () => boolean;
  resetCallbacks: () => void;

  connectToStatusMonitoring: (hubUrl?: string) => Promise<void>;
  disconnectFromStatusMonitoring: () => Promise<void>;
  joinTeamGroup: (teamId: string) => Promise<void>;
  leaveTeamGroup: (teamId: string) => Promise<void>;
  joinShiftInstantGroup: (shiftInstantId: string) => Promise<void>;
  leaveShiftInstantGroup: (shiftInstantId: string) => Promise<void>;
  onOperatorStatusUpdate: (callback: (update: OperatorStatusUpdate) => void) => void;
  onShiftInstantStatusUpdate: (callback: (update: ShiftInstantStatusUpdate) => void) => void;
  onTeamStatusMonitoringUpdate: (callback: (update: TeamStatusMonitoringUpdate) => void) => void;
  resetStatusMonitoringCallbacks: () => void;
  isStatusMonitoringConnected: () => boolean;
}

class SignalRServiceImpl implements SignalRService {
  private connection: HubConnection | null = null;
  private notificationCallbacks: ((notification: Notification) => void)[] = [];
  private alertCallbacks: ((alert: Alert) => void)[] = [];
  private connected = false;
  private currentUserId: string | null = null;

  // Status monitoring properties
  private statusMonitoringConnection: HubConnection | null = null;
  private statusMonitoringConnected = false;
  private operatorStatusCallbacks: ((update: OperatorStatusUpdate) => void)[] = [];
  private shiftStatusCallbacks: ((update: ShiftInstantStatusUpdate) => void)[] = [];
  private teamMonitoringCallbacks: ((update: TeamStatusMonitoringUpdate) => void)[] = [];
  private currentTeamId: string | null = null;
  private currentShiftInstantId: string | null = null;

  async connect(userId: string): Promise<void> {
    // If already connected as this user, do nothing
    if (this.connected && this.currentUserId === userId) {
      return;
    }
    // If connected as a different user, disconnect first
    if (this.connected) {
      this.disconnect();
    }
    this.currentUserId = userId;
    try {
      const { HubConnectionBuilder, LogLevel, HttpTransportType } =
        await import("@microsoft/signalr");
      const URL =
        process.env.NEXT_PUBLIC_SIGNALR_URL ||
        `${API_URL}/notifications-signalr/hub/notification`;
      this.connection = new HubConnectionBuilder()
        .withUrl(`${URL}?userId=${encodeURIComponent(userId)}`, {
          withCredentials: true,
          transport:
            HttpTransportType.WebSockets | HttpTransportType.LongPolling,
          skipNegotiation: false,
        })
        .configureLogging(LogLevel.Information)
        .withAutomaticReconnect([0, 2000, 10000, 30000])
        .build();

      this.connection.off("NewNotification");
      this.connection.off("NewAlert");

      this.connection.on("NewNotification", (notification: Notification) => {
        this.notificationCallbacks.forEach((callback) =>
          callback(notification),
        );
      });

      this.connection.on("NewAlert", (alert: Alert) => {
        this.alertCallbacks.forEach((callback) => callback(alert));
      });

      await this.connection
        .start()
        .then(() => {
          this.connected = true;
        })
        .catch((err: unknown) => {
          this.connected = false;
          if (err instanceof Error) {
            if (err.message.includes("ERR_CERT_AUTHORITY_INVALID")) {
              console.error(
                "SSL Certificate error - please accept the certificate by visiting the URL directly in your browser",
              );
            }
          } else {
            console.error("SignalR connection failed:", err);
          }
          throw err;
        });
    } catch (error) {
      this.connected = false;
      console.error("Failed to connect to SignalR:", error);
      throw error;
    }
  }

  disconnect(): void {
    if (this.connection) {
      this.connection.stop();
      this.connection = null;
    }
    this.connected = false;
    this.currentUserId = null;
  }

  onNotification(callback: (notification: Notification) => void): void {
    this.notificationCallbacks.push(callback);
  }

  onAlert(callback: (alert: Alert) => void): void {
    this.alertCallbacks.push(callback);
  }

  isConnected(): boolean {
    return this.connected;
  }

  resetCallbacks() {
    this.notificationCallbacks = [];
    this.alertCallbacks = [];
  }

  // Status monitoring methods implementation
  async connectToStatusMonitoring(hubUrl?: string): Promise<void> {
    try {
      const { HubConnectionBuilder, LogLevel, HttpTransportType, HubConnectionState } =
        await import("@microsoft/signalr");

      // Check if already connected (similar to index.js pattern)
      if (this.statusMonitoringConnection && this.statusMonitoringConnection.state === HubConnectionState.Connected) {
        return;
      }

      const URL = hubUrl || `${API_URL}/visual-check/hub/status-monitoring`;

      this.statusMonitoringConnection = new HubConnectionBuilder()
        .withUrl(URL, {
          withCredentials: true,
          transport: HttpTransportType.WebSockets | HttpTransportType.LongPolling,
          skipNegotiation: false,
        })
        .configureLogging(LogLevel.Information)
        .withAutomaticReconnect([0, 2000, 10000, 30000])
        .build();

      // Add connection state handlers (similar to index.js)
      this.statusMonitoringConnection.onreconnecting(() => {
        console.log(`${new Date().toLocaleTimeString()} - Status monitoring reconnecting...`);
        this.statusMonitoringConnected = false;
      });

      this.statusMonitoringConnection.onreconnected(() => {
        console.log(`${new Date().toLocaleTimeString()} - Status monitoring reconnected`);
        this.statusMonitoringConnected = true;
      });

      this.statusMonitoringConnection.onclose(() => {
        console.log(`${new Date().toLocaleTimeString()} - Status monitoring disconnected`);
        this.statusMonitoringConnected = false;
      });

      // Set up event listeners
      this.setupStatusMonitoringListeners();

      await this.statusMonitoringConnection.start();
      this.statusMonitoringConnected = true;
      console.log(`${new Date().toLocaleTimeString()} - Connected to status monitoring: ${URL}`);
    } catch (error) {
      this.statusMonitoringConnected = false;
      const errorMessage = error && typeof error === 'object' && 'message' in error ? (error as Error).message : String(error);
      console.log(`${new Date().toLocaleTimeString()} - Status monitoring connection error: ${errorMessage}`);
      throw error;
    }
  }

  private setupStatusMonitoringListeners(): void {
    if (!this.statusMonitoringConnection) return;

    // Clear existing listeners
    this.statusMonitoringConnection.off("OperatorStatusUpdate");
    this.statusMonitoringConnection.off("ShiftInstantStatusUpdate");
    this.statusMonitoringConnection.off("TeamStatusMonitoringUpdate");
    this.statusMonitoringConnection.off("JoinedTeamGroup");
    this.statusMonitoringConnection.off("LeftTeamGroup");
    this.statusMonitoringConnection.off("JoinedShiftInstantGroup");
    this.statusMonitoringConnection.off("LeftShiftInstantGroup");

    // Operator status updates
    this.statusMonitoringConnection.on("OperatorStatusUpdate", (data: OperatorStatusUpdate) => {
      console.warn("Received operator status update:", data);
      this.operatorStatusCallbacks.forEach((callback) => callback(data));
    });

    // Shift instant status updates
    this.statusMonitoringConnection.on("ShiftInstantStatusUpdate", (data: ShiftInstantStatusUpdate) => {
      console.log("Received shift instant status update:", data);
      this.shiftStatusCallbacks.forEach((callback) => callback(data));
    });

    // Team status monitoring updates
    this.statusMonitoringConnection.on("TeamStatusMonitoringUpdate", (data: TeamStatusMonitoringUpdate) => {
      console.log("Received team status monitoring update:", data);
      this.teamMonitoringCallbacks.forEach((callback) => callback(data));
    });

    // Group join/leave confirmations
    this.statusMonitoringConnection.on("JoinedTeamGroup", (teamId: string) => {
      console.log(`Joined team group: ${teamId}`);
    });

    this.statusMonitoringConnection.on("LeftTeamGroup", (teamId: string) => {
      console.log(`Left team group: ${teamId}`);
    });

    this.statusMonitoringConnection.on("JoinedShiftInstantGroup", (shiftInstantId: string) => {
      console.log(`Joined shift instant group: ${shiftInstantId}`);
    });

    this.statusMonitoringConnection.on("LeftShiftInstantGroup", (shiftInstantId: string) => {
      console.log(`Left shift instant group: ${shiftInstantId}`);
    });

    
  }

  async disconnectFromStatusMonitoring(): Promise<void> {
    if (!this.statusMonitoringConnection) return;

    try {
      await this.statusMonitoringConnection.stop();
      console.log(`${new Date().toLocaleTimeString()} - Status monitoring disconnected`);
    } catch (error) {
      const errorMessage = error && typeof error === 'object' && 'message' in error ? (error as Error).message : String(error);
      console.log(`${new Date().toLocaleTimeString()} - Error disconnecting from status monitoring: ${errorMessage}`);
    } finally {
      this.statusMonitoringConnection = null;
      this.statusMonitoringConnected = false;
      this.currentTeamId = null;
      this.currentShiftInstantId = null;
    }
  }

  async joinTeamGroup(teamId: string): Promise<void> {
    if (!this.statusMonitoringConnection || !teamId) return;

    try {
      const { HubConnectionState } = await import("@microsoft/signalr");

      // Wait for connection to be ready (similar to index.js pattern)
      if (this.statusMonitoringConnection.state !== HubConnectionState.Connected) {
        console.log(`${new Date().toLocaleTimeString()} - Connection not ready, current state: ${this.statusMonitoringConnection.state}`);
        return;
      }

      // TODO: TEMP static until we fix data and auth
      await this.statusMonitoringConnection.invoke("JoinUserGroup", "6293ef44-f95a-4e76-a215-6ccdb92d3093");
      this.currentTeamId = teamId;
      console.log(`${new Date().toLocaleTimeString()} - Joined team group: ${teamId}`);
    } catch (error) {
      const errorMessage = error && typeof error === 'object' && 'message' in error ? (error as Error).message : String(error);
      console.log(`${new Date().toLocaleTimeString()} - Error joining team group: ${errorMessage}`);
    }
  }

  async leaveTeamGroup(teamId: string): Promise<void> {
    if (!this.statusMonitoringConnection || !teamId) return;

    try {
      const { HubConnectionState } = await import("@microsoft/signalr");

      // Wait for connection to be ready (similar to index.js pattern)
      if (this.statusMonitoringConnection.state !== HubConnectionState.Connected) {
        console.log(`${new Date().toLocaleTimeString()} - Connection not ready for leaving group, current state: ${this.statusMonitoringConnection.state}`);
        return;
      }

      await this.statusMonitoringConnection.invoke("LeaveUserGroup", "6293ef44-f95a-4e76-a215-6ccdb92d3093");
      if (this.currentTeamId === teamId) {
        this.currentTeamId = null;
      }
      console.log(`${new Date().toLocaleTimeString()} - Left team group: ${teamId}`);
    } catch (error) {
      const errorMessage = error && typeof error === 'object' && 'message' in error ? (error as Error).message : String(error);
      console.log(`${new Date().toLocaleTimeString()} - Error leaving team group: ${errorMessage}`);
    }
  }

  async joinShiftInstantGroup(shiftInstantId: string): Promise<void> {
    if (!this.statusMonitoringConnection || !shiftInstantId) return;

    try {
      const { HubConnectionState } = await import("@microsoft/signalr");

      // Wait for connection to be ready (similar to index.js pattern)
      if (this.statusMonitoringConnection.state !== HubConnectionState.Connected) {
        console.log(`${new Date().toLocaleTimeString()} - Connection not ready for shift instant group, current state: ${this.statusMonitoringConnection.state}`);
        return;
      }

      await this.statusMonitoringConnection.invoke("JoinShiftInstantGroup", shiftInstantId);
      this.currentShiftInstantId = shiftInstantId;
      console.log(`${new Date().toLocaleTimeString()} - Joined shift instant group: ${shiftInstantId}`);
    } catch (error) {
      const errorMessage = error && typeof error === 'object' && 'message' in error ? (error as Error).message : String(error);
      console.log(`${new Date().toLocaleTimeString()} - Error joining shift instant group: ${errorMessage}`);
    }
  }

  async leaveShiftInstantGroup(shiftInstantId: string): Promise<void> {
    if (!this.statusMonitoringConnection || !shiftInstantId) return;

    try {
      const { HubConnectionState } = await import("@microsoft/signalr");

      // Wait for connection to be ready (similar to index.js pattern)
      if (this.statusMonitoringConnection.state !== HubConnectionState.Connected) {
        console.log(`${new Date().toLocaleTimeString()} - Connection not ready for leaving shift instant group, current state: ${this.statusMonitoringConnection.state}`);
        return;
      }

      await this.statusMonitoringConnection.invoke("LeaveShiftInstantGroup", shiftInstantId);
      if (this.currentShiftInstantId === shiftInstantId) {
        this.currentShiftInstantId = null;
      }
      console.log(`${new Date().toLocaleTimeString()} - Left shift instant group: ${shiftInstantId}`);
    } catch (error) {
      const errorMessage = error && typeof error === 'object' && 'message' in error ? (error as Error).message : String(error);
      console.log(`${new Date().toLocaleTimeString()} - Error leaving shift instant group: ${errorMessage}`);
    }
  }

  onOperatorStatusUpdate(callback: (update: OperatorStatusUpdate) => void): void {
    this.operatorStatusCallbacks.push(callback);
  }

  onShiftInstantStatusUpdate(callback: (update: ShiftInstantStatusUpdate) => void): void {
    this.shiftStatusCallbacks.push(callback);
  }

  onTeamStatusMonitoringUpdate(callback: (update: TeamStatusMonitoringUpdate) => void): void {
    this.teamMonitoringCallbacks.push(callback);
  }

  resetStatusMonitoringCallbacks(): void {
    this.operatorStatusCallbacks = [];
    this.shiftStatusCallbacks = [];
    this.teamMonitoringCallbacks = [];
  }

  isStatusMonitoringConnected(): boolean {
    if (!this.statusMonitoringConnection) return false;

    try {
      // Use dynamic import to avoid issues with SSR
      const { HubConnectionState } = require("@microsoft/signalr");
      return this.statusMonitoringConnection.state === HubConnectionState.Connected;
    } catch {
      return this.statusMonitoringConnected;
    }
  }
}

export const signalRService = new SignalRServiceImpl();
